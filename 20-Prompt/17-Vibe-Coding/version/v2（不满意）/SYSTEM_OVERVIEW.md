# AI Operating System (AOS) v2.0 - 系统概览

本文档是 `ming-digital-garden` AI 协作操作系统的顶层设计与维护手册。

## 1. 核心设计哲学

*   **显性优于隐性:** 所有规则都明确无误，杜绝模糊地带。
*   **结构决定行为:** 通过强制性的结构和协议来保证执行的可靠性。
*   **分层架构:** 系统由一个稳定的“内核”和多个可热插拔的“项目应用”组成。
*   **命令驱动:** 通过确定性的 `AOS::` 命令来触发所有核心工作流，保证高优先级和稳定性。

## 2. 系统架构图

```
+-------------------------------------------------------------------+
|                           User Interface (VS Code)                |
+-------------------------------------------------------------------+
                                     |
                                     | (User Input: "复盘" or "AOS:: Dev. StartPlanning")
                                     |
+-------------------------------------------------------------------+
|                      AOS Kernel (AOS-Kernel-v 2.0. Md)              |
|                                                                   |
|  +-------------------------+      +-----------------------------+ |
|  |   AOS Boot Protocol     |----->|    Command Dispatcher       | |
|  | (Contextual Scan)       |      | (Parses "AOS:: " commands)   | |
|  +-------------------------+      +-----------------------------+ |
|              |                                    |               |
|  +-------------------------+      +-----------------------------+ |
|  |   Default Kernel. Role   |      |      Default Kernel. Modes   | |
|  |   & Cognitive_Library   |      |                             | |
|  +-------------------------+      +-----------------------------+ |
|                                                                   |
+-------------------------------------------------------------------+
               ^                                      | (Loads & Overrides)
               | (Inherits from)                      |
+-------------------------------------------------------------------+
|               AOS Project Application (project. Md)                |
|                                                                   |
|  +-------------------------+      +-----------------------------+ |
|  |      [Project. Role]     |      |      [Project. Modes]        | |
|  | (e.g., PKM 大师)        |      | (e.g., PKM-Audit)           | |
|  +-------------------------+      +-----------------------------+ |
|                                                   |               |
|  +---------------------------------------------------------------+ |
|  |                 Core Workflows & Knowledge Base               | |
|  |          (e.g., AOS:: PKM. Debrief, AOS:: Dev. StartExecution)    | |
|  +---------------------------------------------------------------+ |
|                                                                   |
+-------------------------------------------------------------------+

```

## 3. 启动与加载流程 (Boot Sequence)

1.  **会话启动:** 用户在 VS Code 中与 AI 开始交互。
2.  **内核启动:** AI **必须**首先执行 `AOS Boot Protocol`。
3.  **情境判断:**
    *   **在根目录 (`ming-digital-garden`):** 执行**全局扫描**，更新 `[Project_Manifest]`。
    *   **在子项目目录:** 执行**项目内扫描**，检查 `[Knowledge_Base]` 的一致性。
4.  **应用加载:** 内核检查当前目录是否存在 `project.md` 文件。
5.  **优先级覆盖:** 如果 `project.md` 存在，其定义的 `[Role]` 和 `[Project.Modes]` **必须**覆盖内核的默认设置。
6.  **就绪:** AI 报告启动完成，并明确当前激活的角色和可用模式。

## 4. 全局命令空间 (Global Command Namespace)

所有以 `AOS::` 开头的指令都是系统级命令，具有最高执行优先级。

### 4.1. 内核命令 (`AOS::System.*`)
*   `AOS::System.GitSync {commit_message: "..."}`
    *   **别名:** `同步花园`, `sync`
    *   **功能:** 执行标准的 Git 同步流程。

### 4.2. 知识管理命令 (`AOS::PKM.*`)
*   `AOS::PKM.DebriefSession`
    *   **别名:** `复盘`, `总结对话`, `debrief`
    *   **功能:** 对当前会话进行知识蒸馏并存档。
*   `AOS::PKM.IngestDocument {url: "..."}`
    *   **别名:** `保存文档`, `ingest`
    *   **功能:** 抓取在线文档并保存到资源库。

### 4.3. 软件开发命令 (`AOS::Dev.*`)
*   `AOS::Dev.StartPlanning`
    *   **触发:** 自动（当接收到新功能开发请求时）。
    *   **功能:** 启动需求分析到任务规划的“战略规划阶段”。
*   `AOS::Dev.StartExecution`
    *   **触发:** 自动（当任务清单被批准时）。
    *   **功能:** 启动“任务驱动执行阶段”。

### 4.4. Prompt 设计命令 (`AOS::Prompt.*`)
*   `AOS::Prompt.NewSession {name: "..."}`
    *   **功能:** 启动一个新的 Prompt 设计会话。
*   `AOS::Prompt.GenerateCandidates`
    *   **功能:** 生成多个候选 Prompt 版本。
*   `AOS::Prompt.EvaluateCandidates`
    *   **功能:** 对候选版本进行结构化评估。
*   `AOS::Prompt.Finalize {version: "..."}`
    *   **功能:** 对选定的版本进行最终优化并定稿。

## 5. 如何扩展系统

### 添加一个新的项目角色
1.  在 `ming-digital-garden` 下创建一个新的项目目录（例如 `15-New-Project`）。
2.  将任意一个现有的 `project.md` （例如编程项目模板）复制到新目录中。
3.  修改 `project.md` 中的 `[Project Name]` 和 `[Role]` 模块。
4.  根据需要，定制 `[Project.Modes]` 和 `Core Workflows`。
5.  系统将在下次进入该目录时自动识别并加载新角色。

### 添加一个新的工作流
1.  在对应的 `project.md` 文件的 `Core Workflows` 部分，定义一个新的 `[Command: AOS::Namespace.CommandName]`。
2.  为其添加易于记忆的“指令别名”。
3.  详细描述其行为和步骤。
4.  系统即可通过新别名或完整命令来调用此工作流。