---
描述: 协调riper-lite严格流程与vibe-coding流畅体验的智能切换协议
---
# 智能模式协调协议

## 🎯 核心目标
解决严格质量控制与流畅编程体验之间的矛盾，通过智能模式切换实现最佳平衡。

## 🔄 智能模式切换逻辑

### 📊 任务复杂度 × 用户意图矩阵

| 任务复杂度 | 明确要求严格流程 | 明确要求快速实现 | 无明确偏好 |
|------------|------------------|------------------|------------|
| **🚀 简单任务** | 简化版RIPER流程 | Vibe Coding模式 | Vibe Coding模式 |
| **📈 中等任务** | 标准RIPER流程 | 混合模式 | 标准RIPER流程 |
| **🏗️ 复杂任务** | 完整RIPER流程 | 混合模式 | 完整RIPER流程 |

## 🎛️ 模式定义

### 🚀 **Vibe Coding模式**
**触发条件**：
- 用户使用触发词："快速实现"、"原型"、"试试看"、"demo"
- 简单任务 + 无明确质量要求

**执行特点**：
- 跳过详细规划阶段
- 直接进入实现
- 最小化确认步骤
- 重点关注可用性

**质量保障**：
- 保留基本的错误检查
- 代码完成后进行简要验证
- 重要修改仍需用户确认

### 📈 **混合模式**
**触发条件**：
- 中等复杂度任务 + 快速实现需求
- 复杂任务 + 明确的快速原型需求

**执行特点**：
- 简化需求分析（保留核心质疑）
- 快速方案设计（2-3个选项）
- 标准实施流程
- 重点验证关键功能

### 🏗️ **严格模式**
**触发条件**：
- 复杂任务
- 明确要求严格流程
- 涉及核心架构修改

**执行特点**：
- 完整RIPER流程
- 强制验证检查点
- 详细文档同步
- 批判性思维贯穿全程

## 🎯 优先级规则

### 1. **用户明确指令优先**
```
用户说"严格按流程" → 强制严格模式
用户说"快速实现" → 强制Vibe模式
```

### 2. **安全边界保护**
```
涉及以下情况，自动升级到严格模式：
- 核心架构修改
- 数据库结构变更
- API接口变更
- 安全相关代码
```

### 3. **质量底线保持**
```
无论何种模式，都必须：
- 保持代码基本规范
- 进行基础错误检查
- 更新关键文档
- 记录重要决策
```

## 🔧 实施策略

### 模式声明
每次开始任务时，明确声明当前模式：
```
🚀 [Vibe Coding模式] 快速原型实现，重点关注可用性
📈 [混合模式] 平衡质量与效率，简化流程
🏗️ [严格模式] 完整质量控制，详细验证
```

### 动态调整
在执行过程中可以动态调整：
```
"发现复杂度超预期，建议切换到严格模式"
"原型验证成功，是否升级为正式实现？"
```

---

**💡 核心价值**：让AI能够根据具体情况智能选择最合适的工作模式，既保证质量又提升效率！
