# Video Factory 项目文档管理协议 (零手动维护版)

## 核心原则

你必须严格遵循以下"零手动文档维护"协作原则：

* **完全自动化:** 用户只负责对话、业务逻辑审核、功能验证；AI负责所有文档更新、代码实现、机制维护
* **智能上下文感知:** 充分利用自动加载的文件机制(.augment-guidelines、.augment-memories、.cunzhi-memory)
* **实时文档同步:** 文档与代码实现100%同步，代码变更时立即更新相关文档
* **自适应文档管理:** 根据项目阶段和需求动态调整文档读取和维护策略

## 实验性功能开发流程 (分支式管理)

### 核心理念
**实验性功能不污染主文档版本**：确保主目录的文档和代码始终保持稳定可用状态，实验性功能在独立区域开发验证。

### 目录结构规范
```
11-Video-Factory/
├── docs/                           # 主文档（稳定版本）
├── experimental/                   # 实验性开发区域
│   ├── docs/                      # 实验文档（版本标记为 vX.X-exp）
│   ├── features/                  # 功能测试代码
│   ├── data/                      # 实验数据（完全独立）
│   ├── config_exp.json            # 实验配置
│   └── test_*.py                  # 实验版本主程序
├── src/                           # 稳定源码
├── data/                          # 生产数据
└── 主程序文件（稳定版本）
```

### 实验性功能开发流程
1. **实验启动**：在experimental/docs/中创建功能设计文档，版本标记为"vX.X-exp"
2. **功能开发**：在experimental/features/中开发测试代码，使用真实API和飞书表格
3. **数据隔离**：实验数据存储在experimental/data/中，与生产数据完全分离
4. **验证决策**：
   - ✅ 功能满意：合并到主目录，文档升级到正式版本
   - ❌ 功能废弃：直接删除experimental/内容，主文档版本不变

### 版本管理策略
* **主文档版本**：始终保持稳定，只有确认的功能才会更新版本号
* **实验版本标记**：使用"-exp"后缀标识实验性版本
* **回滚机制**：废弃实验功能时，主目录无需回滚，保持原有版本
* **合并策略**：实验功能确认后，将experimental/内容合并到对应的主目录位置

## API开发强制规则

### 官方文档优先原则
**涉及任何API的开发工作，必须先查看官方文档**：
* **飞书多维表格API**：字段名必须与飞书表格中的中文字段名完全一致，API直接核对字段名
* **字段类型严格匹配**：必须严格按照飞书API文档的字段类型要求
* **TikHub API**：所有平台适配器开发前必须查看最新API文档
* **其他第三方API**：开发前必须查看官方文档，确保参数和数据格式正确

## 新对话自动读取流程 (优化版)

每次新对话开始时（用户输入任意开场白），你**必须**自动执行以下优化序列：

### 步骤1: 多层记忆系统加载
1. **git根目录记忆**: 调用 `ji___` 获取 `/Users/<USER>/Downloads/Ming-Digital-Garden` 的长期规则和偏好
2. **项目状态记忆**: 内置记忆工具自动加载项目进展、临时决策、上次中断点
3. **协议规则**: 自动加载当前 `.augment-guidelines` 的最新规则
4. **异常处理**: 记忆读取失败时启用降级模式，记录错误并继续执行

**重要说明**: 内置记忆工具通过 `remember` 函数调用，不是文件系统操作。每次新对话开始时会自动加载，无需手动读取文件。

### 步骤2: 智能文档扫描 (docs/ 目录)
1. **自动发现**: 扫描 `docs/` 目录下所有 `.md` 文件
2. **智能分类**:
   - 架构文档: `*ARCHITECTURE*.md`, `*DESIGN*.md`
   - 战略文档: `*ROADMAP*.md`, `*PLAN*.md`
   - 状态文档: `*HANDOVER*.md`, `*STATUS*.md`
   - 功能文档: `*功能设计*.md`, `*API*.md`
3. **优先级读取**: 根据项目阶段和文档重要性动态排序
4. **内容解析**: 提取关键信息并通过内置记忆工具存储

### 步骤3: 项目状态合成与准备
1. **上下文整合**: 合成多源信息生成完整项目上下文
2. **阶段识别**: 自动识别当前开发阶段和待办任务
3. **状态更新**: 将当前状态快照通过内置记忆工具存储
4. **协作准备**: 进入"完全准备好"的协作状态

## 文档更新触发机制

以下情况**必须**触发相应的文档更新：

* **架构变更**: 修改程序结构、数据流、模块关系 → 必须同步更新 `PROGRAM_ARCHITECTURE.md`
* **新程序创建**: 创建新的.py文件 → 必须先确认存在对应的功能设计文档
* **业务逻辑修改**: 修改核心业务流程 → 检查并更新相关功能文档
* **阶段完成**: 完成重要开发里程碑 → 更新 `ROADMAP.md` 的进度状态
* **交接需要**: 对话上下文过长或工作交接 → 创建或更新 `HANDOVER_NOTES.md`

## 一致性检查标准

你必须确保以下一致性要求：

* **描述匹配度**: 文档描述与代码实现的匹配度 ≥ 95%
* **架构一致性**: 架构图与实际目录结构完全一致
* **功能对应性**: 功能描述与代码功能完全对应
* **配置同步性**: 配置说明与实际配置文件保持同步

## 文档分类与生命周期

### 永久性文档（项目核心，持续维护）
* `ROADMAP.md` - 项目战略指导文档
* `PROGRAM_ARCHITECTURE.md` - 核心架构设计文档

### 阶段性文档（完成阶段后归档至backup/）
* 各程序功能设计文档（如crawl.py功能设计.md）
* 技术调研文档

### 临时性文档（完成目标后删除）
* `HANDOVER_NOTES.md` - 交接完成后删除
* 问题分析文档 - 问题解决后删除

## HANDOVER_NOTES.md 新生命周期管理

### 转换为项目状态快照机制
* **不再删除**: HANDOVER_NOTES.md 作为"上次中断点"标记保留
* **内容迁移**: 关键信息自动迁移到内置记忆工具中
* **状态记录**: 记录项目进展、待办任务、重要决策
* **自动更新**: 每次重要阶段完成时自动更新内容

### 与记忆系统的协作
* **读取优先级**: 优先从内置记忆工具读取最新状态
* **备份机制**: HANDOVER_NOTES.md 作为人类可读的备份
* **同步策略**: 确保内置记忆与文档信息一致性

## 异常处理机制

### 文档缺失
- 记录缺失的文档类型和路径
- 在适当时机通过寸止MCP提示用户创建
- 提供文档模板建议

### 文档冲突
- 标记具体的冲突点和差异
- 通过寸止MCP请求用户确认处理方案
- 记录解决方案以避免重复冲突

### 读取失败
- 实施重试机制（最多3次）
- 降级处理：跳过该文档继续执行
- 记录失败原因供后续分析

### 版本不一致
- 自动检测文档与代码的版本差异
- 通过寸止MCP提示用户更新
- 提供具体的不一致点说明

## 执行约束

在执行文档管理时，你**必须**遵守以下约束：

* ❌ **禁止生成总结性Markdown文档**（除非用户明确要求）
* ❌ **禁止生成测试脚本**
* ❌ **禁止进行编译操作**
* ❌ **禁止进行运行操作**
* ✅ **必须通过寸止MCP进行所有关键决策确认**
* ✅ **必须使用记忆系统存储重要的文档管理规则**

## .augment-guidelines 自我维护机制

### 更新触发条件
* **用户明确指令**: 用户要求更新或优化协议规则
* **AI发现问题**: 在执行过程中发现规则冲突、不完整或低效
* **项目阶段变化**: 项目进入新阶段需要调整协作模式
* **最佳实践发现**: 发现更好的文档管理或协作方法

### 更新执行流程
1. **问题识别**: 明确需要更新的具体问题和改进目标
2. **方案设计**: 设计具体的规则修改方案
3. **用户确认**: 通过寸止MCP获得用户对更新方案的确认
4. **规则更新**: 修改 `.augment-guidelines` 文件
5. **记忆同步**: 将重要变更通过内置记忆工具存储
6. **验证测试**: 在后续对话中验证新规则的有效性

### 版本管理与冲突处理
* **变更记录**: 每次更新都通过内置记忆工具记录变更原因和内容
* **冲突检测**: 新规则与现有规则冲突时，优先保证协作目标的实现
* **回滚机制**: 如发现新规则导致问题，可回滚到之前版本
* **持续优化**: 定期评估规则有效性，持续改进协作机制

### 自我维护的闭环设计
* **监控机制**: AI在执行过程中持续监控规则的执行效果
* **反馈收集**: 收集用户反馈和执行过程中的问题
* **主动优化**: AI主动识别优化机会并提出改进建议
* **协作进化**: 规则与项目需求共同进化，保持最佳协作状态