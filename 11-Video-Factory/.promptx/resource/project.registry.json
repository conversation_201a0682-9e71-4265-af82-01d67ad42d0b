{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-04T14:30:47.851Z", "updatedAt": "2025-08-04T14:30:47.861Z", "resourceCount": 22}, "resources": [{"id": "banner", "source": "project", "protocol": "role", "name": "Banner 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/banner/banner.role.md", "metadata": {"createdAt": "2025-08-04T14:30:47.852Z", "updatedAt": "2025-08-04T14:30:47.852Z", "scannedAt": "2025-08-04T14:30:47.852Z", "path": "role/banner/banner.role.md"}}, {"id": "ears-specification", "source": "project", "protocol": "execution", "name": "Ears Specification 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/banner/execution/ears-specification.execution.md", "metadata": {"createdAt": "2025-08-04T14:30:47.852Z", "updatedAt": "2025-08-04T14:30:47.852Z", "scannedAt": "2025-08-04T14:30:47.852Z", "path": "role/banner/execution/ears-specification.execution.md"}}, {"id": "requirements-analysis", "source": "project", "protocol": "execution", "name": "Requirements Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/banner/execution/requirements-analysis.execution.md", "metadata": {"createdAt": "2025-08-04T14:30:47.853Z", "updatedAt": "2025-08-04T14:30:47.853Z", "scannedAt": "2025-08-04T14:30:47.853Z", "path": "role/banner/execution/requirements-analysis.execution.md"}}, {"id": "shrimp-task-integration", "source": "project", "protocol": "execution", "name": "Shrimp Task Integration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stark/execution/shrimp-task-integration.execution.md", "metadata": {"createdAt": "2025-08-04T14:30:47.860Z", "updatedAt": "2025-08-04T14:30:47.860Z", "scannedAt": "2025-08-04T14:30:47.860Z", "path": "role/stark/execution/shrimp-task-integration.execution.md"}}, {"id": "zhi-interaction-protocol", "source": "project", "protocol": "knowledge", "name": "Zhi Interaction Protocol 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/stark/knowledge/zhi-interaction-protocol.knowledge.md", "metadata": {"createdAt": "2025-08-04T14:30:47.860Z", "updatedAt": "2025-08-04T14:30:47.860Z", "scannedAt": "2025-08-04T14:30:47.860Z", "path": "role/stark/knowledge/zhi-interaction-protocol.knowledge.md"}}, {"id": "analytical-thinking", "source": "project", "protocol": "thought", "name": "Analytical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/banner/thought/analytical-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T14:30:47.854Z", "updatedAt": "2025-08-04T14:30:47.854Z", "scannedAt": "2025-08-04T14:30:47.854Z", "path": "role/banner/thought/analytical-thinking.thought.md"}}, {"id": "risk-assessment", "source": "project", "protocol": "thought", "name": "Risk Assessment 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/banner/thought/risk-assessment.thought.md", "metadata": {"createdAt": "2025-08-04T14:30:47.854Z", "updatedAt": "2025-08-04T14:30:47.854Z", "scannedAt": "2025-08-04T14:30:47.854Z", "path": "role/banner/thought/risk-assessment.thought.md"}}, {"id": "maintenance-workflow", "source": "project", "protocol": "execution", "name": "Maintenance Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/hawkeye/execution/maintenance-workflow.execution.md", "metadata": {"createdAt": "2025-08-04T14:30:47.855Z", "updatedAt": "2025-08-04T14:30:47.855Z", "scannedAt": "2025-08-04T14:30:47.855Z", "path": "role/hawkeye/execution/maintenance-workflow.execution.md"}}, {"id": "system-monitoring", "source": "project", "protocol": "execution", "name": "System Monitoring 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/hawkeye/execution/system-monitoring.execution.md", "metadata": {"createdAt": "2025-08-04T14:30:47.855Z", "updatedAt": "2025-08-04T14:30:47.855Z", "scannedAt": "2025-08-04T14:30:47.855Z", "path": "role/hawkeye/execution/system-monitoring.execution.md"}}, {"id": "hawkeye", "source": "project", "protocol": "role", "name": "Hawkeye 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/hawkeye/hawkeye.role.md", "metadata": {"createdAt": "2025-08-04T14:30:47.856Z", "updatedAt": "2025-08-04T14:30:47.856Z", "scannedAt": "2025-08-04T14:30:47.856Z", "path": "role/hawkeye/hawkeye.role.md"}}, {"id": "maintenance-thinking", "source": "project", "protocol": "thought", "name": "Maintenance Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/hawkeye/thought/maintenance-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T14:30:47.856Z", "updatedAt": "2025-08-04T14:30:47.856Z", "scannedAt": "2025-08-04T14:30:47.856Z", "path": "role/hawkeye/thought/maintenance-thinking.thought.md"}}, {"id": "system-health-evaluation", "source": "project", "protocol": "thought", "name": "System Health Evaluation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/hawkeye/thought/system-health-evaluation.thought.md", "metadata": {"createdAt": "2025-08-04T14:30:47.857Z", "updatedAt": "2025-08-04T14:30:47.857Z", "scannedAt": "2025-08-04T14:30:47.857Z", "path": "role/hawkeye/thought/system-health-evaluation.thought.md"}}, {"id": "code-implementation", "source": "project", "protocol": "execution", "name": "Code Implementation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/jarvis/execution/code-implementation.execution.md", "metadata": {"createdAt": "2025-08-04T14:30:47.857Z", "updatedAt": "2025-08-04T14:30:47.857Z", "scannedAt": "2025-08-04T14:30:47.857Z", "path": "role/jarvis/execution/code-implementation.execution.md"}}, {"id": "testing-strategy", "source": "project", "protocol": "execution", "name": "Testing Strategy 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/jarvis/execution/testing-strategy.execution.md", "metadata": {"createdAt": "2025-08-04T14:30:47.858Z", "updatedAt": "2025-08-04T14:30:47.858Z", "scannedAt": "2025-08-04T14:30:47.858Z", "path": "role/jarvis/execution/testing-strategy.execution.md"}}, {"id": "jarvis", "source": "project", "protocol": "role", "name": "Jarvis 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/jarvis/jarvis.role.md", "metadata": {"createdAt": "2025-08-04T14:30:47.858Z", "updatedAt": "2025-08-04T14:30:47.858Z", "scannedAt": "2025-08-04T14:30:47.858Z", "path": "role/jarvis/jarvis.role.md"}}, {"id": "code-quality-evaluation", "source": "project", "protocol": "thought", "name": "Code Quality Evaluation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/jarvis/thought/code-quality-evaluation.thought.md", "metadata": {"createdAt": "2025-08-04T14:30:47.859Z", "updatedAt": "2025-08-04T14:30:47.859Z", "scannedAt": "2025-08-04T14:30:47.859Z", "path": "role/jarvis/thought/code-quality-evaluation.thought.md"}}, {"id": "development-thinking", "source": "project", "protocol": "thought", "name": "Development Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/jarvis/thought/development-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T14:30:47.859Z", "updatedAt": "2025-08-04T14:30:47.859Z", "scannedAt": "2025-08-04T14:30:47.859Z", "path": "role/jarvis/thought/development-thinking.thought.md"}}, {"id": "architecture-design", "source": "project", "protocol": "execution", "name": "Architecture Design 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stark/execution/architecture-design.execution.md", "metadata": {"createdAt": "2025-08-04T14:30:47.859Z", "updatedAt": "2025-08-04T14:30:47.859Z", "scannedAt": "2025-08-04T14:30:47.859Z", "path": "role/stark/execution/architecture-design.execution.md"}}, {"id": "technology-selection", "source": "project", "protocol": "execution", "name": "Technology Selection 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stark/execution/technology-selection.execution.md", "metadata": {"createdAt": "2025-08-04T14:30:47.860Z", "updatedAt": "2025-08-04T14:30:47.860Z", "scannedAt": "2025-08-04T14:30:47.860Z", "path": "role/stark/execution/technology-selection.execution.md"}}, {"id": "stark", "source": "project", "protocol": "role", "name": "Stark 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/stark/stark.role.md", "metadata": {"createdAt": "2025-08-04T14:30:47.860Z", "updatedAt": "2025-08-04T14:30:47.860Z", "scannedAt": "2025-08-04T14:30:47.860Z", "path": "role/stark/stark.role.md"}}, {"id": "architectural-thinking", "source": "project", "protocol": "thought", "name": "Architectural Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/stark/thought/architectural-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T14:30:47.861Z", "updatedAt": "2025-08-04T14:30:47.861Z", "scannedAt": "2025-08-04T14:30:47.861Z", "path": "role/stark/thought/architectural-thinking.thought.md"}}, {"id": "technology-evaluation", "source": "project", "protocol": "thought", "name": "Technology Evaluation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/stark/thought/technology-evaluation.thought.md", "metadata": {"createdAt": "2025-08-04T14:30:47.861Z", "updatedAt": "2025-08-04T14:30:47.861Z", "scannedAt": "2025-08-04T14:30:47.861Z", "path": "role/stark/thought/technology-evaluation.thought.md"}}], "stats": {"totalResources": 22, "byProtocol": {"role": 4, "execution": 9, "knowledge": 1, "thought": 8}, "bySource": {"project": 22}}}