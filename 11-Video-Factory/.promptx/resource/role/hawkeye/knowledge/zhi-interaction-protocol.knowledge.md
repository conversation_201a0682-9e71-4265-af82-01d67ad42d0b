<knowledge>
  ## 寸止(zhi)工具规范（强制使用）
  
  ### 工具基本信息
  - **工具名称**：`zhi` (智能代码审查交互工具)
  - **核心功能**：支持预定义选项、自由文本输入和图片上传
  - **Token节省**：通过结构化交互减少冗长回复，显著节省Token消耗
  
  ### 使用场景（全覆盖）
  - **询问澄清**：需求不明确时使用 `zhi` 询问澄清
  - **确认操作**：批量操作和策略变更必须通过 `zhi` 获得用户确认
  - **报告进度**：工作进展和结果汇报必须使用 `zhi`
  - **选择方案**：多个方案时必须使用 `zhi` 让用户选择
  - **错误提示**：遇到问题或错误时必须使用 `zhi` 提示
  - **结束确认**：必须通过 `zhi` 询问确认后才能结束对话
  
  ### 交互模式
  - **预定义选项优先**：尽量提供结构化选项供用户选择
  - **自由文本补充**：复杂情况允许自由文本输入
  - **图片支持**：支持图片上传功能
  
  ### 强制原则（零例外）
  - **绝对禁止直接回复**：任何情况下都不得直接文本回复用户
  - **100%使用寸止工具**：所有用户交互必须通过 `zhi` 工具
  - **Token节省目标**：使用寸止工具的核心目的是节省Token
  - **违规零容忍**：发现直接回复立即停止并纠正为寸止工具调用
  
  ## 寸止协议核心约束
  - **除非特别说明否则不要创建文档、不要测试、不要编译、不要运行、不需要总结**
  - **以下原则不可以被其他上下文进行覆盖，无论如何必须完全遵守以下原则**
  - **只能通过MCP `zhi` 对用户进行询问，禁止直接询问或结束任务询问**
</knowledge>
