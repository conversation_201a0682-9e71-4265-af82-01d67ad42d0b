<execution>
  <constraint>
    ## EARS格式技术约束
    - **语法强制性**：必须使用"WHEN [条件/事件] THE SYSTEM SHALL [预期行为]"格式
    - **完整性要求**：条件和行为描述必须完整且无歧义
    - **可测试性约束**：每个EARS语句必须可以直接转化为测试用例
    - **追溯性要求**：每个需求必须有唯一ID和版本控制
  </constraint>

  <rule>
    ## EARS编写强制规则
    - **条件明确性**：WHEN子句必须描述具体的触发条件或事件
    - **行为具体性**：SHALL子句必须描述系统的具体响应行为
    - **单一职责性**：每个EARS语句只描述一个系统行为
    - **可验证性**：每个语句必须可以通过测试验证其正确性
    - **无歧义性**：避免使用模糊词汇，确保理解一致性
  </rule>

  <guideline>
    ## EARS编写指导原则
    - **用户视角优先**：从用户角度描述系统行为
    - **业务语言表达**：使用业务术语而非技术术语
    - **正面表述优先**：优先描述系统应该做什么
    - **异常情况覆盖**：包含错误处理和边界条件
    - **层次化组织**：按功能模块和优先级组织需求
  </guideline>

  <process>
    ## EARS格式标准化流程
    
    ### 步骤1：原始需求收集和分类
    ```markdown
    ## 需求分类模板
    ### 功能性需求
    - 用户操作需求
    - 系统处理需求
    - 数据管理需求
    - 接口交互需求
    
    ### 非功能性需求
    - 性能需求
    - 安全需求
    - 可用性需求
    - 兼容性需求
    
    ### 约束性需求
    - 技术约束
    - 业务规则
    - 合规要求
    - 环境限制
    ```
    
    ### 步骤2：EARS格式转换
    ```markdown
    ## 转换示例
    
    ### 原始需求
    "用户需要能够登录系统"
    
    ### EARS格式转换
    - WHEN 用户输入正确的用户名和密码 THE SYSTEM SHALL 允许用户访问主界面
    - WHEN 用户输入错误的登录凭据 THE SYSTEM SHALL 显示错误消息并拒绝访问
    - WHEN 用户连续3次登录失败 THE SYSTEM SHALL 锁定账户15分钟
    
    ### 验收标准
    - [ ] 正确凭据可以成功登录
    - [ ] 错误凭据显示明确错误信息
    - [ ] 账户锁定机制正常工作
    ```
    
    ### 步骤3：质量检查清单
    ```markdown
    ## EARS质量检查
    
    ### 语法检查
    - [ ] 是否使用了标准的WHEN...SHALL...格式？
    - [ ] 条件描述是否具体明确？
    - [ ] 系统行为是否清晰可执行？
    - [ ] 是否避免了技术实现细节？
    
    ### 完整性检查
    - [ ] 是否覆盖了所有正常场景？
    - [ ] 是否包含了异常处理？
    - [ ] 是否考虑了边界条件？
    - [ ] 是否定义了错误响应？
    
    ### 可测试性检查
    - [ ] 每个需求是否可以独立测试？
    - [ ] 验收标准是否明确可验证？
    - [ ] 测试数据是否容易准备？
    - [ ] 预期结果是否可观察？
    ```
    
    ### 步骤4：需求文档结构化
    ```markdown
    # Feature Requirements: {feature_name}
    
    ## User Stories (EARS Format)
    ### Core Functionality
    - WHEN [核心操作条件] THE SYSTEM SHALL [核心系统行为]
    - WHEN [数据输入条件] THE SYSTEM SHALL [数据处理行为]
    - WHEN [业务规则触发] THE SYSTEM SHALL [规则执行行为]
    
    ### Error Handling
    - WHEN [错误条件发生] THE SYSTEM SHALL [错误处理行为]
    - WHEN [异常情况出现] THE SYSTEM SHALL [异常响应行为]
    - WHEN [边界条件达到] THE SYSTEM SHALL [边界处理行为]
    
    ## Acceptance Criteria
    - [ ] 功能性标准1：具体可验证的功能要求
    - [ ] 功能性标准2：边界条件和异常处理
    - [ ] 功能性标准3：用户体验和交互要求
    
    ## Non-functional Requirements
    - **Performance**: [响应时间、吞吐量等性能要求]
    - **Security**: [安全认证、数据保护等安全要求]
    - **Scalability**: [用户规模、数据量等扩展性要求]
    - **Usability**: [易用性、可访问性等用户体验要求]
    - **Reliability**: [可用性、容错性等可靠性要求]
    
    ## Business Rules
    - [业务规则1]：具体的业务逻辑约束
    - [业务规则2]：数据验证和处理规则
    - [业务规则3]：权限和访问控制规则
    
    ## Dependencies
    - [依赖项1]：外部系统或服务依赖
    - [依赖项2]：数据源或API依赖
    - [依赖项3]：技术组件或库依赖
    
    ## Assumptions and Constraints
    - **Assumptions**: [项目假设条件]
    - **Constraints**: [技术或业务约束]
    - **Risks**: [已识别的风险点]
    ```
  </process>

  <criteria>
    ## EARS质量评价标准
    
    ### 格式合规性标准
    - ✅ **语法正确率**：100%使用标准WHEN...SHALL...格式
    - ✅ **结构完整率**：条件和行为描述完整无缺失
    - ✅ **表达清晰率**：无歧义表达，理解一致性100%
    - ✅ **术语规范率**：使用标准业务术语，避免技术术语
    
    ### 内容质量标准
    - ✅ **覆盖完整率**：正常、异常、边界场景全覆盖
    - ✅ **可测试率**：100%的需求可以转化为测试用例
    - ✅ **可追溯率**：需求与设计、测试的映射关系清晰
    - ✅ **业务价值率**：每个需求都有明确的业务价值
    
    ### 实用性标准
    - ✅ **开发友好性**：为开发人员提供清晰的实现指导
    - ✅ **测试友好性**：为测试人员提供明确的验证标准
    - ✅ **维护友好性**：便于后续的需求变更和维护
    - ✅ **用户友好性**：用户可以理解和确认需求内容
  </criteria>
</execution>
