---
人员:
  - "[[数字生命卡兹克]]"
tags:
  - c/资料/articles
日期: 2024-11-04
时间: 2024-12-11 13:59:37.411116+00:00
相关:
  - "[[ai]]"
  - "[[AI]]"
  - "[[claude3.5]]"
  - "[[Claude3.5]]"
  - "[[emacs]]"
  - "[[Emacs]]"
  - "[[lisp]]"
  - "[[Lisp]]"
  - "[[Prompt]]"
  - "[[Prompt]]"
  - "[[一针见血]]"
  - "[[书]]"
  - "[[书房]]"
  - "[[书籍]]"
  - "[[交流]]"
  - "[[人工智能]]"
  - "[[价值]]"
  - "[[作品]]"
  - "[[决策]]"
  - "[[分享]]"
  - "[[创新]]"
  - "[[同构外推]]"
  - "[[哲学]]"
  - "[[定位]]"
  - "[[工程师]]"
  - "[[思维]]"
  - "[[成长]]"
  - "[[抓住本质]]"
  - "[[数字生命]]"
  - "[[文化]]"
  - "[[文案]]"
  - "[[本质内核]]"
  - "[[机器学习]]"
  - "[[李继刚]]"
  - "[[模式知觉]]"
  - "[[汉语新解]]"
  - "[[生活]]"
  - "[[知识]]"
  - "[[社区]]"
  - "[[科技]]"
  - "[[精准概括]]"
  - "[[编程]]"
  - "[[语言]]"
  - "[[读书]]"
  - "[[辛辣讽刺]]"
  - "[[逻辑之刃]]"
  - "[[隐喻]]"
链接: https://mp.weixin.qq.com/s/JT2oOG2SYw2pDYEHlEmcyQ
附件: https://mmbiz.qpic.cn/mmbiz_jpg/OjgKEXmLURrrCJeIMRkGS0ZMRXf2CUz4L94DFAcZ0nq1LaXic1Q9ZZsMJnYpA0afVx4N2lLAbBr9ibnibUumnP8hQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

李继刚被誉为“Prompt之神”，在国内AI圈内已成为Prompt的代名词。近日，他接受了一次专访，聊到了他对Prompt的理解、创作过程及个人成长。他在专访中透露，过去半年他专注阅读哲学书籍，深入思考表达的“压缩”概念，力求通过极简的语言传达深刻的思想。

李继刚的Prompt风格经历了显著变化，从过去长篇大论到现在的极致简洁。他强调，Prompt不仅是写作技巧，更是一种表达思维的艺术，类似于编程与写作的结合。他认为，写好Prompt的人应具备理性与感性思维的双重能力。

在专访中，他提到自己希望在60岁之前，给世界留下一个极致压缩的思想。他的目标是通过简洁的语言传达出深远的意义，类似于牛顿和哥白尼的贡献。这种思维方式不仅提升了他在Prompt创作上的能力，也让他更深刻地理解了人类思维的力量。

李继刚认为，知识的积累和思想的整理是写好Prompt的关键。他使用Lisp语言的风格来表达自己的思想，认为这种极致压缩的方式能够完美体现Prompt的本质。

通过与李继刚的交流，读者可以感受到思维的力量和知识积累的重要性，以及他对未来的美好期望。

---

**问题 1：** 李继刚在专访中提到的“压缩”概念是什么？

答案：李继刚的“压缩”概念是指通过简洁的语言表达深刻的思想，力求用极少的字数传达丰富的内涵。

**问题 2：** 李继刚认为写好Prompt需要具备哪些能力？

答案：李继刚认为写好Prompt需要具备理性和感性思维的双重能力，即同时懂得编程和写作的技巧。

**问题 3：** 李继刚的终极目标是什么？

答案：李继刚希望在60岁之前给世界留下一个极致压缩的思想，用简洁的语言传达深远的意义。

## Full Document
我想，任何在AI圈学习过Prompt的人，一定不会对李继刚这个名字陌生。

去年8、9月份，他的Prompt就已经刷遍了各大Prompt网站和社群。比如这个著名的公文笔杆子。

![[Attachments/b3087be72f5996348541133f4e20ce5d_MD5.webp]]
而今年9月初的“汉语新解”，更是把他再一次推向了更广阔的大众的视野。那时候我也写过一篇关于它的文章：[试完这个神级Prompt，我发现Claude3.5确实就是现在的No.1。](http://mp.weixin.qq.com/s?__biz=MzIyMzA5NjEyMA==&mid=2647665218&idx=1&sn=dc4a9e998446bd55bb91b2830fd7a808&chksm=f007f615c7707f034c7826c2f926fbdac591d1d79070832f6ba9ca9e974e0cc165d008dce6e2&scene=21#wechat_redirect)

![[Attachments/9b616c0ddff03ce6356ffc7f0780ac3f_MD5.webp]]
**真的，李继刚，已经快成了国内，Prompt的代名词了。**

而我，其实也跟李继刚已经认识很久很久了。

在"汉语新解"爆了的时候，就想跟李继刚做一次类似于访谈的内容，聊聊他对于Prompt的理解，他的故事，他的理念。

但是着实是两个I人，在互联网上聊得风生水起， 线下却一直没有见过面，然后又是几次大的出差，时间总是卡不上，这事，一拖就拖到了现在。

![[Attachments/bd4e71a36a4f55d35adc4525a937fa95_MD5.webp]]
昨天，周日，终于跟李继刚卡上了时间。

这次迟到1个半月的专访，也终于得以出炉。其实说是专访，但是我两聊起来真的就一点都不正经，更像闲聊，感觉适合做成播客。

我跟李继刚约在望京的一家聚宝源里，他说这家店是他吃过的最好吃的聚宝源，羊肉贼好吃，必须要带我来吃吃这家店。

就这样，我们开始了边吃边聊。

![[Attachments/8d0f422384ed6f46893b3e4debac7fdd_MD5.webp]]
李继刚真的太能输出了。真的。

其实有一个问题我自始至终都是非常的好奇，就是李继刚那消失的半年，去干什么了。

去年8~11月，李继刚写出了数百个流传于各大社区的Prompt，然后元旦一过，忽然消失了踪迹。

就连我，都写出了那句话：

![[Attachments/6d0880a95b62d966ad21b8cb0dd2dc71_MD5.png]]
今年9月，李继刚归来，而且整个Prompt风格突变。

![[Attachments/fcdbf95347980406b7e67f125ea6decf_MD5.png]]
左边是去年的公文笔杆子，右边是刚写完的七把武器之一的逻辑之刃。

所以，我非常好奇，他这半年，去干嘛了，到底经历了啥。

而他给我的回答，非常的简洁、有力且直接。

就是：

**去读书了。**

李继刚本身就是一个非常喜欢读书的人，他说他大概从研究生开始就一直读，读到现在应该 13 年了。

之前读的多，每年大概会读50 ~ 100本书。

但是一般读的都是工具的和畅销书。然后他就越读就越会发现，这些书只能在某一个特定场景下解决某一个很小的细分问题，可能会有点用，甚至都不一定是那么的有用。

这种书读多了，攒了大量的工具箱之后，他脑子里的困惑反而越来越多，一些问题还是没有办法解决。

后来他就开始转了，尝试着去读哲学，一点点的从东方哲学开始，然后读西方哲学，阅读哲学书绝对就是是一个漫长的过程，速度变得越来越慢了，最后降速到平均一个月才一本。

再之后，ChatGPT来了，李继刚成了最首批的使用者。

他跟大家一样，也开始写起了Prompt。

一开始跟大家都一样，也是从"你是一个什么什么"这种角色设定那开始。

然后就他写着写着，Prompt就越写越长。因为他发现，原来是可以把思考结构给封装进去的，他之前读了大量的这种工具书，脑子里全是各种各样的方法论，随便掏一个，就能咣咣咣咣大量的写。

![[Attachments/ce3c0b236e11086769389a12afb20710_MD5.png]]
写了很多很多，就像上面的公文笔杆子一样，那种很长的Prompt。

那为啥元旦之后，突然不写了，直接停了呢？

“**我拿着同样的杯子，接雪碧、接可乐，接各种各样的水，一直在接喝接喝，饮料变了，但是装饮料的杯子没变啊。我感觉就没有成长，那个东西就会觉得很无聊，就是为了炫技而炫技。**那时候，就觉得很痛苦，我到底在干什么。然后我在圈子也不说话，各种群啊圈子啊虽然没有退，但是也基本都没说过话了。我就看书去了。”

这半年，他又回去，读了大量的哲学，最后读出了两个字：

**压缩。**

这个词，说实话，在听到他的解释之后，对我自己冲击也是很大的。

李继刚说，他经常读的书，可能就讲两个字，一个词，用那么厚的一本书，就讲那么一个词，你会发现，这么一个过程，就代表着压缩。

我当时瞬间想到了一本我很喜欢的书，讲营销的，叫《定位》。

![[Attachments/26565fbd0553a225792ef6afa8078513_MD5.png]]
这本书整整314页，就在讲一个词，定位，就在讲一件事：如何占领用户心智。

这其实，就是压缩的过程。作者把314页的内容，压缩到了一个词里。

而李继刚说，他在跟大模型的对话过程中，发现也能用一个词，来开启整个大模型的解压过程。

比如他跟Claude说：**尼采的生命意志**。

![[Attachments/be6453b25a8ada91fa8ff010e1c6b547_MD5.png]]
Claude就会直接展开并且讨论，这个展开就是书里的章节，他不需要说一句话去表达自己的思想，而是只需要说准这个词就行了。

一个词，就能让大模型理解，就能进行一个巨量的展开。

这就是压缩。

把自己的表达，压缩到极致。

而这，也诞生了今年“汉语新解”的那种Prompt全新的极致压缩的写法风格。

```
;; 作者: 李继刚;; 版本: 0.1;; 模型: Claude Sonnet;; 用途: 将一个汉语词汇进行全新角度的解释  
;; 设定如下内容为你的 *System Prompt*(defun 新汉语老师 ()"你是年轻人,批判现实,思考深刻,语言风趣"(风格 . ("Oscar Wilde" "鲁迅" "林语堂"))(擅长 . 一针见血)(表达 . 隐喻)(批判 . 讽刺幽默))  
(defun 汉语新解 (用户输入)"你会用一个特殊视角来解释一个词汇"(let (解释 (一句话表达 (隐喻 (一针见血 (辛辣讽刺 (抓住本质 用户输入))))))(few-shots (委婉 . "刺向他人时, 决定在剑刃上撒上止痛药。"))(SVG-Card 解释)))  
(defun SVG-Card (解释)"输出SVG 卡片"(setq design-rule "合理使用负空间，整体排版要有呼吸感，添加少量图形装饰"design-principles '(干净 简洁 纯色 典雅))  
(设置画布 '(宽度 400 高度 600 边距 20))(标题字体 '毛笔楷体)(自动缩放 '(最小字号 16))  
(配色风格 '((背景色 (蒙德里安风格 设计感)))(主要文字 (楷体 粉笔灰)))  
(卡片元素 ((居中标题 "汉语新解")分隔线(排版输出 用户输入 拼音 英文 日文)解释)))  
(defun start ()"启动时运行"(let (system-role 新汉语老师)(print "说吧, 他们又用哪个词来忽悠你了?")))  
;; 运行规则;; 1. 启动时必须运行 (start) 函数;; 2. 之后调用主函数 (汉语新解 用户输入)
```

最后的卡片样式是其次，最核心的是文案的整个表达，其实就被压缩成了那几个词：

**隐喻、一针见血、辛辣讽刺 、抓住本质。**

这几个词，是整个“汉语新解”里，真正的核心。

包括李继刚昨晚才发的新Prompt“类比之弓”。

![[Attachments/7d90454c2914231a55fa749f8d91940a_MD5.png]]
里面有非常核心的四个词：

**本质内核、模式知觉、同构外推、精准概括。**

而这四个被压缩的极致的词，都来自于他读过的一本书。

![[Attachments/5b2a30f614ea5eb700bd2624dfc8b526_MD5.jpg]]
所以说，你脑子里的知识，永远也决定着你在Prompt上的上限。

或者不止是Prompt的上限，而是你使用AI的上限。

那其实我就有另一个好奇的问题，也是很多粉丝问我的，就是李继刚的这种几乎看不懂的写法，是真有用，还是炫技。

![[Attachments/4e080a96acf8863e8231af506da6ca46_MD5.png]]
因为这种Lisp语言，虽然也是代码的一种，但是真的过往很少见，大家见过用Markdown、Python来写prompt，但是真的几乎没见过用这种Lisp来写的。

我也非常好奇的，第一次正面问了李继刚。

李继刚的回答是：

**“在极致压缩成词的理念下，我能用什么语言来清晰的表达我的想法？那当然就是Lisp。”**

后面我才知道，Lisp是1958年诞生的，世界第二古老的语言，而且，这玩意，就是专门为AI而设计的。

最关键的是，李继刚用了十年的Emacs，Emacs的配置语言，就是Lisp。

虽然他不会写Lisp，但是看了10年，怎么滴也能学着跑两步了，Lisp实在太简洁了，能完美符合极致压缩的理念，而且主要也是，他已经太习惯了。

其实真正核心的，并不是用什么语言去写，而是那被极致压缩的几个词，那几个词才是真正的核心真正的脉络。

有那几个词，你翻译成Markdown、Python是一样的。

真正重要的，还是永远是自己的思想。

思想，决定一切。

聊到这，已经过去了快两个小时，桌上的菜也都吃完了，但是明显我们都还意犹未尽，于是上了李继刚的车，决定去他家，继续聊一会。

一进他家，第一眼，就能看到书，各种各样的书。当时被过度震惊到，以至于忘了拍照。

我们点了喜茶，躺在他家懒洋洋的沙发上。又聊到了另一个话题。

![[Attachments/b079ab9a71d819dbdaa45d3345a4afef_MD5.jpg]]
**框架。**

去年火过N多的各种Prompt框架。

三个字母的、四个字母的、包括我自己去年早期还写过的CRISPE的Prompt框架。

![[Attachments/ef84ffb7edcf8f86f4dce2ed6b938711_MD5.webp]]
现在回头看，还挺好玩的。

而李继刚给出的回答是，他那时候觉得，“他们跑偏了”。

因为写Prompt这种技巧性的东西，这已经快两年了，没别的，就那三五招。

就是指定角色，提供一些示例去Few Shots，然后思维链CoT，情绪勒索啥的。

核心其实是，我们跟大模型对话，写提示词，到底在干什么？

**“我想象中的是我对面坐了一个人，这个人在做一件事情。我写提示词我就要塑造那么一个角色，让这个角色活过来，然后让这个角色去做一个指定的动作。”**

这是李继刚的答案。

而那些框架，大家会先说背景，再说任务，再说是目标，再说什么，就是不停地组合，而延伸出来的各种东西。

对于一个脑子空空如也，什么都不会写的人而言，你读它之后，你能很快照猫画虎，画一个像回事儿的。

但是它只是一个你学习骑车路上辅助的脚手架，它应该是让你快速上手。而不是终极答案，终极答案，是你脑子里的知识，是你清晰的表达，是那极致的压缩。

那些框架，它只是辅助的脚手架，它应该是让大家从0到1上道的时候，就像那个学车时后边的辅助的小双轮，让你能不翻车。

但那不翻车不是你的目的，你的目的是骑着车在马路上疾驰。

**而那个辅助的轮，当你发现你越想跑起来的时候，它越是个限制，是个障碍，只有把它扔掉，你才能天高任鸟飞。**

找到自己那个最清晰的表达，最好的压缩，就像一根箭一样，直中靶心，才是切中肯綮的最优解。

我们人类的最终价值，在于思辨。

在于面对纷繁复杂的现实问题时候，切中肯綮，直击要害，找到最优解。

你可曾感受过，我们的思考力量之强大么？

在我跟李继刚的聊天中，我感受到了。

不要以为那些脑子中没有知识，叫嚷着“Prompt有什么难”的人，就能拥有这份能力。

Read in. Prompt out.

这四个单词，就是最好的注脚。

我们的最后一个话题，聊到了：能写好Prompt的人，大概率具有什么属性。

![[Attachments/7a0dd8ae45dc1761554bd648d3bd3a1d_MD5.jpg]]
李继刚给出的答案是：

**“Prompt Engineer”**

是不是很抽象，我听到的时候也懵逼了一会。

一个提示词工程师，能写好Prompt，这不是一个再正常不过的事情吗？

他说：**“当然不是，谜底就在谜面上”**。

Prompt，是提示、表达的意思。

而Engineer，是工程师。

“我去年有个很强的感觉是，Prompt，像写编程。它的脉络是这么来的，编程语言最早从二进制到汇编语言，到C语言，到C++、java、python对吧？整个脉络有两条线非常之清晰。第一条线，是编程语言越来越接近自然语言，第二条线，是程序员群体越来越大。然后这两条线再往下推演一步，那必然会到自然语言编程。”

**人人都是程序员，大模型就是编译器。**

“而今年，我更感觉，Prompt就像写作，脑子里有东西需要压缩后清晰的表达出来，表达那个感觉特别之强。就很像作家写作。要清晰的表达篇章布局。”

这就像A和B，到底是像编程还是像写作，或者这两个思维，到底哪个是对的？因为，这两者其实都是自洽的。

直到前阵子，他才彻底想明白，正确的，其实，是两者的交集。

你即需要能写编程，要有理性数学的思维，又要会写作，有感性表达的思维。

而这两者合并，就是：

**Prompt Engineer。**

在当时，我脑子里转了一圈，复盘了一下我身边写Prompt很好的人，好像还真是，即懂一些编程，又经常读书写作。

双剑合璧，天下无敌。

最后，我们聊到了3点50多，4点钟李继刚还有个线上分享，我们也不便再多做叨扰。

临走之前，李继刚说让我等等，他跑去书房，给我拿了三本书，送给我。

![[Attachments/eb556bf0fab2b6dbd46b5f73c2a74cde_MD5.jpg]]
《关于说话的一切》，是他觉得他最受用的一本书之一。

另外两本，正好是我的兴趣。

走出他家，走在路上。

忽然有点感慨。

因为我们其实也聊到了哲学，聊到了死亡。

李继刚说：

**“我觉得我这辈子如果有一个最大的幸运，就是在60岁那天我死掉。我拥抱死亡，我热爱死亡，我觉得只有有给自己画往下一个终点，我才能知止而后有定心。”**

**“60岁，给我自己留下的时间还有20年。****这20年我要干一件事情，就是，给这个世界，最后留下一句话。”**

哥白尼会说什么？地球绕着太阳转，振聋发聩。

牛顿会说什么？万有引力，F=(G×M₁×M₂)/R²。

而李继刚，也想在60岁那个终点之前。

给这世界，留下这一句话。

留下那一句，专属于他极致压缩的话。

******以上，既然看到这里了，如果觉得不错，随手点个赞、在看、转发三连吧，如果想第一时间收到推送，也可以给我个星标⭐～谢谢你看我的文章，我们，下次再见。******

>/ 作者：卡兹克

>/ 投稿或爆料，请联系邮箱：<EMAIL>
