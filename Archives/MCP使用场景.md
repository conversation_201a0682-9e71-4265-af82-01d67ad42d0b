---
tags:
  - s/archive
---

# Augment 工具集速查指南

一份为 augment 量身定制的、简洁明了的工具使用指南。

### 核心原则
*   **描述目标，而非工具**：请用自然语言说明您想达成的目的。
*   **组合使用以应对复杂任务**：一个复杂目标可能需要多个工具协同完成。
*   **关键步骤会寻求确认**：我会在关键节点向您寻求反馈和批准。

### 工具参考

| 分类 | 工具名称 | 核心功能与用途 | 关键意图触发词 |
| :--- | :--- | :--- | :--- |
| **交互** | `mcp-feedback-enhanced` | 在交互节点进行确认、获取反馈、请求批准。 | (每次交互结束时) |
| **思维** | `sequential-thinking` | 结构化地思考、分解问题、规划方案。 | `思考一下`, `分析`, `规划`, `梳理一下` |
| **代码与文件** | `codebase-retrieval` | 高层次地理解代码库的结构与逻辑。 | `理解代码`, `分析架构`, `代码结构是怎样的` |
| | `view` | 读取一个或多个文件的具体内容。 | `查看文件`, `读一下`, `显示内容` |
| | `str-replace-editor` | 对文件进行精确的查找与替换修改。 | `修改`, `编辑`, `更新`, `替换` |
| | `save-file` | 使用指定内容创建新文件。 | `创建文件`, `新建`, `保存为` |
| | `remove-files` | 安全地删除一个或多个文件。 | `删除`, `移除` |
| | `launch-process` | 执行终端命令、运行脚本或启动测试。 | `运行`, `执行`, `测试`, `安装`, `调试` |
| **任务管理** | `Shrimp Task Manager` | 管理复杂项目，支持任务规划、拆分与追踪。 | `规划项目`, `管理任务`, `追踪进度` |
| | `Augment Task Tools` | 提供简单的任务创建(`add_tasks`)、更新(`update_tasks`)和查看(`view_tasklist`)。 | `创建任务`, `更新状态`, `查看任务列表` |
| **知识查询** | `Context7` / `deepwiki-mcp` | 查询官方API文档、最佳实践和内部知识库。 | `查文档`, `这个API怎么用`, `查内部Wiki` |
| | `mcp.memory` / `remember` | 跨会话存储和回忆信息，实现长期记忆。 | `记住这个`, `保存偏好`, `回忆一下` |
| **搜索调研** | `GitHub Search Tools` | 在GitHub上搜索代码、仓库和项目信息。 | `在GitHub上找`, `搜索仓库`, `找代码示例` |
| | `Exa Search` / `web-search` | 进行深度学术研究或常规的网页搜索。 | `搜索`, `查找资料`, `谷歌一下` |
| | `firecrawl-mcp` | 从指定网页抓取内容并进行结构化提取。 | `抓取这个页面`, `获取URL内容`, `爬取` |
| **测试** | `Playwright` | 自动化浏览器操作，用于UI测试和交互。 | `测试网页`, `点击按钮`, `填写表单`, `截图` |
| **API文档** | `TikHub API Docs` | 查询抖音/TikTok相关的API文档。 | `TikHub API`, `抖音接口`, `TikTok文档` |
| | `Feishu API Docs` | 查询飞书/Lark相关的API文档。 | `飞书API`, `Lark文档`, `多维表格` |
| **设计** | `@magicuidesign/mcp` | 查找或生成现代化的UI组件和设计资源。 | `找UI组件`, `设计按钮`, `搜索图标` |

### 场景化工具组合推荐

#### 开发与调研
| 工具组合 | 适用场景/阶段 | 关键流程 |
| :--- | :--- | :--- |
| `sequential-thinking` + `codebase-retrieval` | 深度分析与方案设计 | 1. 用思维链分析问题<br>2. 检索相关代码以理解现状 |
| `Exa Search` + `GitHub搜索` + `Context7` | 全面技术调研 | 1. 深度搜索核心概念<br>2. 查找开源实现参考<br>3. 确认官方文档规范 |
| `codebase-retrieval` + `str-replace-editor` + `launch-process` | 快速功能实现与迭代 | 1. 理解相关代码<br>2. 精确修改<br>3. 运行测试验证 |

#### 测试与修复
| 工具组合                                                 | 适用场景/阶段  | 关键流程                                    |
| :--------------------------------------------------- | :------- | :-------------------------------------- |
| `launch-process` + `Playwright`                      | 全面功能测试   | 1. 运行单元/集成测试<br>2. 进行端到端UI自动化测试         |
| `错误分析` + `codebase-retrieval` + `str-replace-editor` | Bug调试与修复 | 1. 分析错误日志<br>2. 定位并理解问题代码<br>3. 实施修复并验证 |



---


# 工具集速查指南

一份简洁明了的工具使用指南。

### 核心原则
*   **描述目标，而非工具**：请用自然语言说明您想达成的目的。
*   **组合使用以应对复杂任务**：一个复杂目标可能需要多个工具协同完成。
*   **关键步骤会寻求确认**：我会在关键节点向您寻求反馈和批准。

### 工具参考

| 分类         | 工具名称                        | 核心功能与用途                                                            | 关键意图触发词                          |
| :--------- | :-------------------------- | :----------------------------------------------------------------- | :------------------------------- |
| **思维**     | `sequential-thinking`       | 结构化地思考、分解问题、规划方案。                                                  | `思考一下`, `分析`, `规划`, `梳理一下`       |
| **代码与文件**  | `codebase-retrieval`        | 高层次地理解代码库的结构与逻辑。                                                   | `理解代码`, `分析架构`, `代码结构是怎样的`       |
|            | `view`                      | 读取一个或多个文件的具体内容。                                                    | `查看文件`, `读一下`, `显示内容`            |
|            | `str-replace-editor`        | 对文件进行精确的查找与替换修改。                                                   | `修改`, `编辑`, `更新`, `替换`           |
|            | `save-file`                 | 使用指定内容创建新文件。                                                       | `创建文件`, `新建`, `保存为`              |
|            | `remove-files`              | 安全地删除一个或多个文件。                                                      | `删除`, `移除`                       |
|            | `launch-process`            | 执行终端命令、运行脚本或启动测试。                                                  | `运行`, `执行`, `测试`, `安装`, `调试`     |
| **任务管理**   | `Augment Task Tools`        | 提供简单的任务创建 (`add_tasks`)、更新 (`update_tasks`) 和查看 (`view_tasklist`)。 | `创建任务`, `更新状态`, `查看任务列表`         |
| **知识查询**   | `Context7` / `deepwiki-mcp` | 查询官方 API 文档、最佳实践和内部知识库。                                            | `查文档`, `这个API怎么用`, `查内部Wiki`     |
|            | `remember`                  | 跨会话存储和回忆信息，实现长期记忆。                                                 | `记住这个`, `保存偏好`, `回忆一下`           |
| **搜索调研**   | `GitHub Search Tools`       | 在 GitHub 上搜索代码、仓库和项目信息。                                            | `在GitHub上找`, `搜索仓库`, `找代码示例`     |
|            | `Exa Search` / `web-search` | 进行深度学术研究或常规的网页搜索。                                                  | `搜索`, `查找资料`, `谷歌一下`             |
|            | `firecrawl-mcp`             | 从指定网页抓取内容并进行结构化提取。                                                 | `抓取这个页面`, `获取URL内容`, `爬取`        |
| **测试**     | `Playwright`                | 自动化浏览器操作，用于 UI 测试和交互。                                              | `测试网页`, `点击按钮`, `填写表单`, `截图`     |
| **API 文档** | `TikHub API Docs`           | 查询抖音/TikTok 相关的 API 文档。                                            | `TikHub API`, `抖音接口`, `TikTok文档` |
|            | `Feishu API Docs`           | 查询飞书/Lark 相关的 API 文档。                                              | `飞书API`, `Lark文档`, `多维表格`        |
| **设计**     | `@magicuidesign/mcp`        | 查找或生成现代化的 UI 组件和设计资源。                                              | `找UI组件`, `设计按钮`, `搜索图标`          |

### 场景化工具组合推荐

#### 开发与调研
| 工具组合 | 适用场景/阶段 | 关键流程 |
| :--- | :--- | :--- |
| `sequential-thinking` + `codebase-retrieval` | 深度分析与方案设计 | 1. 用思维链分析问题<br>2. 检索相关代码以理解现状 |
| `Exa Search` + `GitHub搜索` + `Context7` | 全面技术调研 | 1. 深度搜索核心概念<br>2. 查找开源实现参考<br>3. 确认官方文档规范 |
| `codebase-retrieval` + `str-replace-editor` + `launch-process` | 快速功能实现与迭代 | 1. 理解相关代码<br>2. 精确修改<br>3. 运行测试验证 |

#### 测试与修复
| 工具组合                                                 | 适用场景/阶段  | 关键流程                                    |
| :--------------------------------------------------- | :------- | :-------------------------------------- |
| `launch-process` + `Playwright`                      | 全面功能测试   | 1. 运行单元/集成测试<br>2. 进行端到端 UI 自动化测试         |
| `错误分析` + `codebase-retrieval` + `str-replace-editor` | Bug 调试与修复 | 1. 分析错误日志<br>2. 定位并理解问题代码<br>3. 实施修复并验证 |
