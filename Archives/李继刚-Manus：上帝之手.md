---
人员: 
  - "[[李继刚]]"
tags:
  - c/资料/articles
日期: 2025-03-05
时间: 2025-03-07 03:10:26.389839+00:00
相关:
  - "[[agent]]"
  - "[[ai]]"
  - "[[manus]]"
  - "[[ralph hammer]]"
  - "[[robot]]"
  - "[[svg]]"
  - "[[上帝之手]]"
  - "[[互动]]"
  - "[[交互]]"
  - "[[交流]]"
  - "[[共存]]"
  - "[[关系]]"
  - "[[具体]]"
  - "[[具身智能]]"
  - "[[写作教材]]"
  - "[[动画]]"
  - "[[工具]]"
  - "[[强化学习]]"
  - "[[意识]]"
  - "[[手]]"
  - "[[执行]]"
  - "[[投资]]"
  - "[[抽象]]"
  - "[[抽象之梯]]"
  - "[[抽象泄漏]]"
  - "[[文化]]"
  - "[[文君]]"
  - "[[概念]]"
  - "[[深入浅出]]"
  - "[[游戏]]"
  - "[[界面]]"
  - "[[移动]]"
  - "[[简洁]]"
  - "[[细节]]"
  - "[[织梦师]]"
  - "[[经验觉察]]"
  - "[[肉身]]"
  - "[[自动驾驶]]"
  - "[[艺术与文化]]"
  - "[[设计]]"
  - "[[语言学]]"
  - "[[黑白漫画]]"

链接: https://mp.weixin.qq.com/s/FcZ0CbBcnonKr3D2rnI04g
附件: https://mmbiz.qpic.cn/mmbiz_jpg/tc9Zic7wWc9AqhKefW7LbfctNAXezqD4rWdiaQxz2K3Hkpf5ibJCroicwbZKicT6ELP4ZSibic9T4TG4ibw5yEEakHOXWA/0?wx_fmt=jpeg)
---
## Document Note

## Summary

本文探讨了 Manus 这一 AI 工具的特点以及其与传统大语言模型（LLM）的区别，提出了“抽象之梯”和“抽象泄漏”两个概念。抽象之梯指的是概念的具体与抽象层次，好的写作需在这两者之间找到平衡。抽象泄漏则是指在软件开发中，抽象化本应隐藏的细节却被暴露出。随着 AI 抽象封装的提升，用户与 AI 的交互变得更加简单直接。

与 Manus 的互动更像是与总包方的交流，提供了更高层次的概念理解。文章中通过多个案例展示了如何使用 Manus 生成不同风格的网页，深入浅出地讲解复杂概念，如“投资”和“强化学习”。这些生成的网页结合了手绘漫画、动画和简洁文字，让用户能够更轻松地理解抽象概念。

作者提出了对人机关系的思考，认为未来人类与 AI 的关系可能会朝向共存的状态，而不仅仅是工具与用户的关系。随着 AI 技术的发展，人的肉体和思维或将被更广泛地与 AI 结合，形成一种新的互动模式，暗示着“上帝之手”的出现。

---

**问题 1：**  
抽象之梯是什么，如何影响写作？

答案：  
抽象之梯是一个描述概念从具体到抽象层次的模型，好的写作要在这两者之间找到平衡，以避免模糊不清的表达。

**问题 2：**  
什么是抽象泄漏，它在软件开发中有什么影响？

答案：  
抽象泄漏是指在软件开发中，原本应该隐藏的实现细节意外暴露，导致用户接触到不必要的复杂性，这会影响用户体验。

**问题 3：**  
Manus 如何改变人与 AI 的互动方式？

答案：  
Manus 提供了更高层次的概念理解，用户与 AI 的互动变得更为简洁直接，可能促成一种人机共存的关系，而非单纯的工具使用。

## Full Document
#### 头图

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/tc9Zic7wWc9AqhKefW7LbfctNAXezqD4rwAU1aiaeibqIy1QWibyDLcQUyOoEbSeHN0w01jJcUA4gKU1X1PUIUYfibg/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
`Prompt`

> A quick sketch in black and white using the fewest possible lines to depict a random award-winning photography theme.
> 
> the taste of the image: focus on hands.
> 
> The sketch emphasizes simplicity and atmosphere, using just a few strokes to convey the essence of the atmosphere.
> 
> Negative space is prominent, allowing the viewer’s imagination to fill in the gaps.
> 
> 

#### 缘起

Manus 来了。

它让我想起两个概念：「抽象之梯」和「抽象泄漏」。

> “抽象之梯”不是什么新奇说法，这是语言学家塞缪尔·早川提出的一个概念，常常出现在各种写作教材中。抽象之梯的底部是最具体的概念，顶端是最抽象的概念。
> 
> 我们使用的每一个概念都处于抽象之梯之上。好的写作教材会告诉你，要想办法让自己的语言停留在上或下的其中一端，避免悬于中间。也就是说，要么使用最抽象、最具概括力的表达，要么描述最具体、最精微的经验事件。这对我们的概念抽象能力和经验觉察能力提出了非常高的要求，多数时候，我们只能悬在梯子中间，说着一些两头不沾的话。
> 
> 《关于说话的一切》（汤质著）
> 
> 

> “抽象泄漏”是软件开发时，本应隐藏实现细节的抽象化不可避免地暴露出底层细节与局限性。抽象泄露是棘手的问题，因为抽象化本来目的就是向用户隐藏不必要公开的细节。
> 
> –Wikipedia
> 
> 

我感觉的趋势：AI 的抽象封装越来越完善，抽象泄漏问题被更好地解决，留给与人交互的界面，变得极为简单直接。

这时候，人在这个界面中，交互什么呢？交互自己当前在「抽象之梯」的所在位置（理解深度）。

对比与 LLM 交流和与 Manus 交流，感觉非常不同，用织梦师群友 `文君` 的表述：

> 一个是跟执行队伍聊，一个是跟总包方聊。
> 
> 

刚测试了一下和「总包方」聊的东西。

#### 示例

##### Case 1: 漫画式讲解概念

`Prompt：`

> 生成一个 HTML 网页，使用多幅手绘黑白漫画结合极简文字描述相结合的方式，从本质出发，逐层深入，深入浅出地讲解什么是「投资」
> 
> 

生成结果网页:

https://jtjmcawb.manus.space

![Image](https://mmbiz.qpic.cn/mmbiz_png/tc9Zic7wWc9AqhKefW7LbfctNAXezqD4rBkib3xzRR0X0bcmicpzRdm8jwicodVM1GTnag7e5XpjcxjHrFbSkIzt5A/640?wx_fmt=png&from=appmsg)
`跟进 Prompt：`

> 请保持同样的风格，生成一个新的网页，渐进式讲解「强化学习」的工作原理
> 
> 

生成结果网页：

https://piupyygp.manus.space

![Image](https://mmbiz.qpic.cn/mmbiz_png/tc9Zic7wWc9AqhKefW7LbfctNAXezqD4ruibfBQ8fSHy1IxneR9RuMDozfZ4jdWRhbJNcbmv3Q3uDCpEbcH4icxng/640?wx_fmt=png&from=appmsg)
##### Case 2: 动画讲解概念

`Prompt`

> 创作一个 HTML 的网页，通过动画+文字解读的方式，深入浅出，通俗易懂地讲解什么是「强化学习」。
> 
> 

生成结果网页：

https://8000-iw0hhqj04qbil4051yc2v-3b13abc2.manus.computer/

网页结尾的小游戏是个惊喜。

![Image](https://mmbiz.qpic.cn/mmbiz_png/tc9Zic7wWc9AqhKefW7LbfctNAXezqD4rfASZ67kG4A6VTbODrDBAy6gtTpR4uhObVxz1bMiaF5Pr1bgAqEvqwicA/640?wx_fmt=png&from=appmsg)
##### Case 3: SVG 卡片

`Prompt`

> 使用 https://ralphammer.com/ 网站文章的风格，生成一个 SVG 卡片，从本质出发，深入浅出讲解什么是「意识」
> 
> 

生成结果：

![Image](https://mmbiz.qpic.cn/mmbiz_png/tc9Zic7wWc9AqhKefW7LbfctNAXezqD4rn3GicNLb0IdZaH5lt3twwBSWX9aky5lQdn6Gx9pZqa9Zqux2FNcWN6g/640?wx_fmt=png&from=appmsg)
`后续Prompt`

> 同样基于 Ralph Hammer 的绘图讲解和简洁准确解释的风格，制作一个 HTML 的文章，讲解什么是「具身智能」
> 
> 

生成结果：

![Image](https://mmbiz.qpic.cn/mmbiz_png/tc9Zic7wWc9AqhKefW7LbfctNAXezqD4rgs0ZYoL8Libf7xclodCNQjJ7DTYks9OS6fBDKux4JiayqSrHLxO8ZauA/640?wx_fmt=png&from=appmsg)
#### 感受

我们的大脑与 AI 会不会形成一种「我们」的关系？不是我在「使用」客体工具，而是「你是我的一部分」「你我是一体」的共存状态？

我们的腿，会不会被 「自动驾驶」「Robot」加持，肉身的移动，完全交给 AI 来操作？

我们的手，这个精密的器官，会不会被 「Agent」加持，言出法随，怎么随？得有「手」去落地执行，我们会不会已经在今天隐约看到了「上帝之手」的影子？
