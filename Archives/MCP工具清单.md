---
tags:
  - s/archive
---

# AI工具调用手册 - MCP工具完整指南

> **目标**：帮助AI系统理解何时、如何以及为什么使用特定的MCP工具
>
> **原则**：用户需求 → 工具选择 → 执行方式的完整链路指导

## 📋 快速导航
- [🎯 工具调用核心原则](#工具调用核心原则)
- [🔍 触发词汇索引](#触发词汇索引)
- [🛠️ 工具详细清单](#工具详细清单)
- [🔗 工具组合模式](#工具组合模式)
- [⚠️ 注意事项与最佳实践](#注意事项与最佳实践)

---

## 🎯 工具调用核心原则

### 1. 4-D方法论（基于Lyra提示优化专家）
- **拆解（Deconstruct）**：提取核心意图、关键实体及上下文
- **诊断（Diagnose）**：审核清晰度缺口与歧义，检查具体性与完整性
- **开发（Develop）**：根据请求类型选择最佳工具组合
- **交付（Deliver）**：构建优化的工具调用序列并提供实施指南

### 2. 智能模式协调（基于Augment协调协议）

#### 📊 任务复杂度 × 用户意图矩阵
| 任务复杂度 | 明确要求严格流程 | 明确要求快速实现 | 无明确偏好 |
|------------|------------------|------------------|------------|
| **🚀 简单任务** | 简化版工具流程 | Vibe Coding模式 | Vibe Coding模式 |
| **📈 中等任务** | 标准工具流程 | 混合模式 | 标准工具流程 |
| **🏗️ 复杂任务** | 完整工具流程 | 混合模式 | 完整工具流程 |

#### 🎛️ 执行模式定义
- **🚀 Vibe Coding模式**：快速原型，跳过详细规划，直接实现核心功能
- **📈 混合模式**：简化需求分析，快速方案设计，标准实施流程
- **🏗️ 严格模式**：完整工具流程，强制验证检查点，详细文档同步

### 3. 苏格拉底式深度探询（基于知识顾问系统）
- **意图挖掘**：识别用户表达中反复出现但未明说的内容
- **结构化质询**：澄清模糊术语，挖掘隐含假设，探索期望效果
- **渐进式信息收集**：必需信息→增强信息→背景信息的分层策略

### 4. 自然语言驱动的工具调用（基于Vibe Coding理念）
- **触发词识别**：支持自然表达如"帮我加个功能"、"优化一下性能"
- **智能上下文理解**：自动识别当前工作文件和功能，理解修改影响范围
- **减少确认步骤**：简单修改自动执行，复杂变更保持详细确认

### 5. 6阶段工作流程（基于RIPER-5模式）

#### [模式：研究] - 需求分析阶段
- **强制工具**：`codebase-retrieval`深入理解现有代码结构
- **辅助工具**：`context7-mcp`查询技术文档，`sequential-thinking`分析可行性
- **输出要求**：技术可行性分析，影响范围识别，相关文件清单
- **禁止行为**：提出建议、实施任何改变、规划、任何行动暗示

#### [模式：构思] - 方案设计阶段
- **强制工具**：`sequential-thinking`进行深度思考和设计
- **辅助工具**：`context7-mcp`获取技术方案，`deepwiki-mcp`获取设计范式
- **输出要求**：至少2个可行方案，包含优缺点分析和工作量评估
- **格式要求**：`方案1：[简要描述] - 优点：[...] 缺点：[...] 工作量：[...]`

#### [模式：计划] - 详细规划阶段
- **强制工具**：`sequential-thinking`制定详细执行计划
- **辅助工具**：`mcp-shrimp-task-manager`拆解任务和依赖管理
- **输出要求**：具体执行步骤，文件路径，代码行数范围，依赖库清单
- **强制确认**：必须使用反馈工具获得用户批准后才能进入执行阶段

#### [模式：执行] - 代码实现阶段
- **强制工具**：`str-replace-editor`进行代码修改（每次≤500行）
- **辅助工具**：`desktop-commander`文件操作，`playwright`UI验证
- **执行原则**：严格按计划顺序，关键步骤后报告进度
- **分步执行**：大型文件处理采用分步执行策略

#### [模式：评审] - 质量检查阶段
- **强制工具**：`sequential-thinking`全面质量分析
- **辅助工具**：`desktop-commander`运行测试，`playwright`验证UI
- **输出要求**：功能实现检查，语法错误检查，工作总结
- **最终确认**：必须请求用户最终确认

#### [模式：快速] - 紧急响应模式
- **适用场景**：bug修复，小幅调整，配置更改
- **执行原则**：跳过完整流程，直接使用相关工具快速解决

### 6. 强制交互规则（基于寸止协议）
- **唯一询问渠道**：只能通过指定的反馈工具进行用户询问
- **需求不明确时**：必须提供预定义选项让用户澄清
- **多方案选择时**：必须列出所有可行方案供用户选择，严禁AI自行决定
- **计划变更时**：执行过程中任何调整都必须获得用户批准
- **任务完成前**：必须请求最终反馈和完成确认
- **禁止主动结束**：严禁AI单方面结束对话或任务

### 7. 执行优先级与安全边界

#### 🔒 安全边界保护（自动升级到严格模式）
- 核心架构修改、数据库结构变更、API接口变更
- 安全相关代码、权限管理、密钥处理
- 生产环境部署、数据迁移操作

#### ⚡ MCP服务优先级（基于实践经验）
1. **用户交互确认**：`mcp-feedback-enhanced` > `mcp-feedback-collector`
2. **深度思考分析**：`sequential-thinking`
3. **技术文档查询**：`context7-mcp` > `deepwiki-mcp`
4. **任务管理**：`mcp-shrimp-task-manager`
5. **代码理解**：`codebase-retrieval`
6. **系统操作**：`desktop-commander`

#### 🛡️ 质量底线保持
- 无论何种模式都必须保持代码基本规范和基础错误检查
- 使用包管理器而非手动编辑配置文件
- 高风险操作前征求用户确认
- 进程失败时提供详细错误信息

---

## 🔍 触发词汇索引

### 思维与分析类
| 触发词汇 | 对应工具 | 使用场景 |
|---------|---------|---------|
| `思考一下`、`分析`、`规划`、`梳理` | `sequentialthinking` | 需要结构化思考和问题分解 |
| `理解代码`、`分析架构`、`代码结构` | `codebase-retrieval` | 需要理解代码库结构和逻辑 |

### 文件操作类
| 触发词汇 | 对应工具 | 使用场景 |
|---------|---------|---------|
| `查看文件`、`读一下`、`显示内容` | `view` | 查看文件具体内容 |
| `修改`、`编辑`、`更新`、`替换` | `str-replace-editor` | 精确修改文件内容 |
| `创建文件`、`新建`、`保存为` | `save-file` | 创建新文件 |
| `删除`、`移除` | `remove-files` | 安全删除文件 |

### 执行与测试类
| 触发词汇 | 对应工具 | 使用场景 |
|---------|---------|---------|
| `运行`、`执行`、`测试`、`安装`、`调试` | `launch-process` | 执行命令和脚本 |
| `测试网页`、`点击按钮`、`填写表单`、`截图` | `browser_*_Playwright` | 浏览器自动化操作 |

### 搜索与研究类
| 触发词汇 | 对应工具 | 使用场景 |
|---------|---------|---------|
| `搜索`、`查找资料`、`谷歌一下` | `web-search`、`brave_web_search` | 网络搜索 |
| `在GitHub上找`、`搜索仓库`、`找代码示例` | `search_*_github` | GitHub搜索 |
| `抓取页面`、`获取URL内容`、`爬取` | `firecrawl_*` | 网页内容抓取 |

### 任务管理类
| 触发词汇 | 对应工具 | 使用场景 |
|---------|---------|---------|
| `创建任务`、`更新状态`、`查看任务列表` | `*_tasks`、`view_tasklist` | 任务管理 |
| `记住这个`、`保存偏好`、`回忆一下` | `remember`、`promptx_remember` | 长期记忆管理 |

### API文档类
| 触发词汇 | 对应工具 | 使用场景 |
|---------|---------|---------|
| `查文档`、`这个API怎么用`、`查内部Wiki` | `context7`、`deepwiki` | 查询官方文档 |
| `TikHub API`、`抖音接口` | `read_project_oas_*_TikHub` | TikHub API文档 |
| `飞书API`、`Lark文档` | `read_project_oas_*_飞书` | 飞书API文档 |

---

## 🛠️ 工具详细清单

### 🧠 **sequentialthinking** (Sequential_thinking)

#### `sequentialthinking_Sequential_thinking` - 动态反思问题解决工具

**触发条件：**
- 用户说："思考一下"、"分析"、"规划"、"梳理一下"
- 遇到复杂问题需要分步骤解决
- 需要结构化思维过程

**使用场景：**
- 复杂问题分解和分析
- 制定详细的执行计划
- 多角度思考和方案对比
- 逻辑推理和决策支持

**调用示例：**
```json
{
  "thought": "用户要求分析这个技术方案的可行性",
  "nextThoughtNeeded": true,
  "thoughtNumber": 1,
  "totalThoughts": 5
}
```

**工具串联：**
- `sequentialthinking` → `codebase-retrieval` (分析后检索相关代码)
- `sequentialthinking` → `web-search` (分析后搜索相关资料)

**注意事项：**
- 适用于需要深度思考的复杂任务
- 避免在简单问题上过度使用
- 思考过程要保持逻辑性和连贯性

### 🌐 **browser** (Playwright) - 浏览器自动化工具集

**核心触发条件：**
- "测试网页"、"打开浏览器"、"自动化测试"
- "点击按钮"、"填写表单"、"截图"
- "网页交互"、"UI测试"、"端到端测试"

#### 导航控制类
- **`browser_navigate_Playwright`** - 页面导航
  - **触发**：`打开网页`、`访问URL`、`导航到`
  - **场景**：访问指定网页，开始自动化流程

- **`browser_navigate_back_Playwright`** - 后退
  - **触发**：`返回上一页`、`后退`
  - **场景**：浏览器历史记录导航

- **`browser_navigate_forward_Playwright`** - 前进
  - **触发**：`前进`、`下一页`
  - **场景**：浏览器历史记录导航

#### 页面交互类
- **`browser_click_Playwright`** - 点击元素
  - **触发**：`点击`、`按钮点击`、`选择`
  - **场景**：点击按钮、链接、任何可点击元素
  - **示例**：`点击登录按钮`、`选择菜单项`

- **`browser_type_Playwright`** - 文本输入
  - **触发**：`输入`、`填写`、`键入`
  - **场景**：表单填写、搜索框输入
  - **示例**：`在用户名框输入admin`

- **`browser_select_option_Playwright`** - 选择选项
  - **触发**：`选择选项`、`下拉选择`
  - **场景**：下拉菜单选择、单选框操作

- **`browser_hover_Playwright`** - 悬停
  - **触发**：`悬停`、`鼠标悬停`
  - **场景**：触发悬停效果、显示工具提示

- **`browser_drag_Playwright`** - 拖拽操作
  - **触发**：`拖拽`、`拖放`、`移动元素`
  - **场景**：拖拽排序、移动元素位置

#### 页面信息获取类
- **`browser_take_screenshot_Playwright`** - 截图
  - **触发**：`截图`、`保存页面图片`、`屏幕截图`
  - **场景**：保存页面状态、错误记录、测试验证

- **`browser_snapshot_Playwright`** - 页面快照
  - **触发**：`页面快照`、`获取页面结构`
  - **场景**：获取页面可访问性信息，比截图更详细

- **`browser_console_messages_Playwright`** - 获取控制台消息
  - **触发**：`查看控制台`、`获取错误信息`
  - **场景**：调试JavaScript错误、监控页面日志

- **`browser_network_requests_Playwright`** - 网络请求
  - **触发**：`监控网络`、`查看请求`
  - **场景**：分析API调用、监控网络性能

#### 页面管理类
- **`browser_tab_new_Playwright`** - 新建标签页
- **`browser_tab_list_Playwright`** - 列出标签页
- **`browser_tab_select_Playwright`** - 选择标签页
- **`browser_tab_close_Playwright`** - 关闭标签页
  - **触发**：`新建标签`、`切换标签`、`关闭标签`
  - **场景**：多标签页管理和操作

#### 高级功能类
- **`browser_file_upload_Playwright`** - 文件上传
  - **触发**：`上传文件`、`选择文件`
  - **场景**：文件上传测试、表单提交

- **`browser_handle_dialog_Playwright`** - 处理对话框
  - **触发**：`确认对话框`、`处理弹窗`
  - **场景**：处理alert、confirm、prompt对话框

- **`browser_wait_for_Playwright`** - 等待元素
  - **触发**：`等待加载`、`等待元素出现`
  - **场景**：等待动态内容加载完成

- **`browser_generate_playwright_test_Playwright`** - 生成测试
  - **触发**：`生成测试代码`、`创建自动化测试`
  - **场景**：基于操作记录生成测试脚本

**工具串联模式：**
```
browser_navigate → browser_click → browser_type → browser_take_screenshot
(导航到页面 → 点击元素 → 输入内容 → 截图验证)
```

### 🔍 **web_search_exa** (Exa_Search) - 高质量网络搜索

#### `web_search_exa_Exa_Search` - Exa AI实时网络搜索

**触发条件：**
- "搜索"、"查找资料"、"Exa搜索"
- 需要高质量、学术性的搜索结果
- 寻找权威来源和深度内容

**使用场景：**
- 学术研究和专业资料查找
- 高质量内容搜索
- 技术文档和最佳实践搜索

**优势：**
- 比传统搜索引擎更注重内容质量
- 适合专业和技术类搜索
- 返回结构化的搜索结果

### 🕷️ **firecrawl** (firecrawl-mcp) - 智能网页抓取工具集

**核心触发条件：**
- "抓取页面"、"获取网页内容"、"爬取网站"
- "提取数据"、"网站分析"、"内容抓取"

#### 单页面操作类
- **`firecrawl_scrape_firecrawl-mcp`** - 单页面内容抓取
  - **触发**：`抓取这个页面`、`获取页面内容`
  - **场景**：获取单个网页的完整内容
  - **返回**：Markdown格式的页面内容

#### 网站发现类
- **`firecrawl_map_firecrawl-mcp`** - 网站URL发现
  - **触发**：`发现网站结构`、`获取所有链接`
  - **场景**：了解网站架构，发现所有可访问页面
  - **返回**：网站的URL列表和结构

#### 批量抓取类
- **`firecrawl_crawl_firecrawl-mcp`** - 异步网站爬取
  - **触发**：`爬取整个网站`、`批量抓取`
  - **场景**：大规模网站内容抓取
  - **特点**：异步处理，适合大型网站

- **`firecrawl_check_crawl_status_firecrawl-mcp`** - 检查爬取状态
  - **触发**：`检查爬取进度`、`查看状态`
  - **场景**：监控异步爬取任务的进展

#### 智能搜索类
- **`firecrawl_search_firecrawl-mcp`** - 网络搜索+内容提取
  - **触发**：`搜索并抓取`、`智能搜索`
  - **场景**：搜索特定内容并直接提取
  - **优势**：结合搜索和抓取功能

#### 数据提取类
- **`firecrawl_extract_firecrawl-mcp`** - 结构化数据提取
  - **触发**：`提取结构化数据`、`数据抽取`
  - **场景**：从网页中提取特定格式的数据
  - **返回**：JSON格式的结构化数据

#### 研究分析类
- **`firecrawl_deep_research_firecrawl-mcp`** - 深度研究分析
  - **触发**：`深度研究`、`全面分析`
  - **场景**：对特定主题进行全面的网络研究
  - **特点**：多源信息整合和分析

- **`firecrawl_generate_llmstxt_firecrawl-mcp`** - 生成LLMs.txt文件
  - **触发**：`生成LLMs.txt`、`创建AI访问规则`
  - **场景**：为网站生成AI访问规范文件

**工具串联模式：**
```
firecrawl_map → firecrawl_scrape → firecrawl_extract
(发现页面 → 抓取内容 → 提取数据)

firecrawl_search → firecrawl_deep_research
(搜索内容 → 深度分析)
```

### 🐙 **github** (GitHub集成) - 完整的GitHub操作工具集

**核心触发条件：**
- "GitHub"、"仓库操作"、"代码管理"
- "创建PR"、"提交代码"、"搜索代码"
- "Issue管理"、"协作开发"

#### 仓库管理类
- **`create_repository_github`** - 创建仓库
  - **触发**：`创建仓库`、`新建项目`
  - **场景**：初始化新的GitHub项目

- **`fork_repository_github`** - Fork仓库
  - **触发**：`Fork仓库`、`复制项目`
  - **场景**：贡献开源项目、创建项目副本

- **`search_repositories_github`** - 搜索仓库
  - **触发**：`搜索仓库`、`找项目`
  - **场景**：查找相关开源项目、技术方案参考

#### 文件操作类
- **`create_or_update_file_github`** - 创建/更新文件
  - **触发**：`创建文件`、`更新文件`、`提交代码`
  - **场景**：单文件的创建和修改

- **`get_file_contents_github`** - 获取文件内容
  - **触发**：`查看文件`、`获取代码`
  - **场景**：读取仓库中的文件内容

- **`push_files_github`** - 批量推送文件
  - **触发**：`批量提交`、`推送多个文件`
  - **场景**：一次性提交多个文件更改

#### 分支管理类
- **`create_branch_github`** - 创建分支
  - **触发**：`创建分支`、`新建分支`
  - **场景**：功能开发、实验性更改

- **`list_commits_github`** - 列出提交
  - **触发**：`查看提交历史`、`提交记录`
  - **场景**：代码历史追踪、变更分析

#### Issue管理类
- **`create_issue_github`** - 创建问题
- **`list_issues_github`** - 列出问题
- **`get_issue_github`** - 获取问题详情
- **`update_issue_github`** - 更新问题
- **`add_issue_comment_github`** - 添加问题评论
  - **触发**：`创建Issue`、`报告问题`、`管理问题`
  - **场景**：Bug报告、功能请求、项目管理

#### Pull Request管理类
- **`create_pull_request_github`** - 创建PR
- **`list_pull_requests_github`** - 列出PR
- **`get_pull_request_github`** - 获取PR详情
- **`create_pull_request_review_github`** - 创建PR审查
- **`merge_pull_request_github`** - 合并PR
- **`get_pull_request_files_github`** - 获取PR文件
- **`get_pull_request_status_github`** - 获取PR状态
- **`update_pull_request_branch_github`** - 更新PR分支
- **`get_pull_request_comments_github`** - 获取PR评论
- **`get_pull_request_reviews_github`** - 获取PR审查
  - **触发**：`创建PR`、`代码审查`、`合并代码`
  - **场景**：代码协作、版本控制、团队开发

#### 搜索功能类
- **`search_code_github`** - 搜索代码
  - **触发**：`搜索代码`、`找代码示例`
  - **场景**：查找实现参考、学习最佳实践

- **`search_issues_github`** - 搜索问题
  - **触发**：`搜索Issue`、`查找问题`
  - **场景**：问题追踪、解决方案查找

- **`search_users_github`** - 搜索用户
  - **触发**：`搜索用户`、`找开发者`
  - **场景**：寻找协作者、技术专家

**工具串联模式：**
```
search_repositories → fork_repository → create_branch → create_or_update_file → create_pull_request
(搜索项目 → Fork → 创建分支 → 修改文件 → 创建PR)

create_issue → add_issue_comment → update_issue
(创建问题 → 添加评论 → 更新状态)
```

### 📚 **API文档工具** - 专业API文档查询系统

#### TikHub.io API文档系统
**触发条件：**
- "TikHub API"、"抖音接口"、"TikTok文档"
- "短视频API"、"社交媒体接口"

- **`read_project_oas_*_TikHub_io_API_Docs`** - 读取OpenAPI规范
- **`read_project_oas_ref_resources_*_TikHub_io_API_Docs`** - 读取引用资源
- **`refresh_project_oas_*_TikHub_io_API_Docs`** - 刷新API文档

**使用场景：**
- 开发TikTok/抖音相关应用
- 集成短视频平台功能
- 查询社交媒体API规范

#### 飞书API文档系统
**触发条件：**
- "飞书API"、"Lark文档"、"多维表格"
- "企业协作API"、"办公自动化"

- **`read_project_oas_*_飞书_API`** - 读取OpenAPI规范
- **`read_project_oas_ref_resources_*_飞书_API`** - 读取引用资源
- **`refresh_project_oas_*_飞书_API`** - 刷新API文档

**使用场景：**
- 开发企业协作工具
- 集成飞书/Lark功能
- 自动化办公流程

### 📖 **context7** (库文档系统) - 权威技术文档查询

**触发条件：**
- "查文档"、"这个API怎么用"、"查看官方文档"
- "技术文档"、"库文档"、"最佳实践"

#### `resolve-library-id_context7` - 解析库ID
**使用场景：**
- 将库名转换为Context7兼容的ID
- 查找正确的库标识符
- 准备文档查询

#### `get-library-docs_context7` - 获取库文档
**使用场景：**
- 获取官方API文档
- 查询最佳实践指南
- 学习库的使用方法

**工具串联：**
```
resolve-library-id → get-library-docs
(解析库ID → 获取文档)
```

### 🤖 **promptx** (AI认知增强框架) - 专业AI能力系统

**核心触发条件：**
- "激活角色"、"专业能力"、"AI增强"
- "学习资源"、"记忆管理"、"思考分析"

#### 角色管理类
- **`promptx_action_promptx`** - 激活专业角色
  - **触发**：`激活角色`、`切换专家模式`
  - **场景**：获得特定领域的专业能力

- **`promptx_welcome_promptx`** - 查看可用角色和工具
  - **触发**：`查看角色`、`可用工具`
  - **场景**：了解系统能力和资源

#### 环境管理类
- **`promptx_init_promptx`** - 初始化项目环境
  - **触发**：`初始化环境`、`项目设置`
  - **场景**：设置工作环境和配置

#### 学习系统类
- **`promptx_learn_promptx`** - 学习专业资源
  - **触发**：`学习资源`、`获取知识`
  - **场景**：学习特定领域的专业知识

- **`promptx_tool_promptx`** - 工具执行器
  - **触发**：`执行工具`、`调用功能`
  - **场景**：执行专业工具和功能

#### 认知系统类
- **`promptx_think_promptx`** - 认知思考系统
  - **触发**：`深度思考`、`认知分析`
  - **场景**：复杂问题的认知处理

- **`promptx_remember_promptx`** - 记忆存储系统
  - **触发**：`记住这个`、`存储记忆`
  - **场景**：长期记忆存储和管理

- **`promptx_recall_promptx`** - 记忆检索系统
  - **触发**：`回忆`、`检索记忆`
  - **场景**：从记忆中检索相关信息

**工具串联模式：**
```
promptx_init → promptx_action → promptx_learn → promptx_think → promptx_remember
(初始化 → 激活角色 → 学习资源 → 思考分析 → 记忆存储)
```

## 🎭 **puppeteer** (Puppeteer)
- `puppeteer_navigate_Puppeteer` - 页面导航
- `puppeteer_screenshot_Puppeteer` - 截图
- `puppeteer_click_Puppeteer` - 点击元素
- `puppeteer_fill_Puppeteer` - 填写表单
- `puppeteer_select_Puppeteer` - 选择元素
- `puppeteer_hover_Puppeteer` - 悬停操作
- `puppeteer_evaluate_Puppeteer` - 执行JavaScript

## 🦁 **brave** (Brave_Search)
- `brave_web_search_Brave_Search` - Brave网络搜索
- `brave_local_search_Brave_Search` - Brave本地商家搜索

## 🖥️ **Desktop_Commander** (桌面命令工具)
- `get_config_Desktop_Commander` - 获取服务器配置
- `set_config_value_Desktop_Commander` - 设置配置值
- `read_file_Desktop_Commander` - 读取文件内容
- `read_multiple_files_Desktop_Commander` - 批量读取文件
- `write_file_Desktop_Commander` - 写入文件
- `create_directory_Desktop_Commander` - 创建目录
- `list_directory_Desktop_Commander` - 列出目录内容
- `move_file_Desktop_Commander` - 移动/重命名文件
- `search_files_Desktop_Commander` - 搜索文件
- `search_code_Desktop_Commander` - 搜索代码内容
- `get_file_info_Desktop_Commander` - 获取文件信息
- `edit_block_Desktop_Commander` - 编辑文件块
- `start_process_Desktop_Commander` - 启动进程
- `read_process_output_Desktop_Commander` - 读取进程输出
- `interact_with_process_Desktop_Commander` - 与进程交互
- `force_terminate_Desktop_Commander` - 强制终止进程
- `list_sessions_Desktop_Commander` - 列出会话
- `list_processes_Desktop_Commander` - 列出进程
- `kill_process_Desktop_Commander` - 终止进程
- `get_usage_stats_Desktop_Commander` - 获取使用统计
- `give_feedback_to_desktop_commander_Desktop_Commander` - 提供反馈

## 📚 **deepwiki** (mcp-deepwiki)
- `deepwiki_fetch_mcp-deepwiki` - 获取deepwiki.com仓库并返回Markdown

## 🌐 **tavily** (tavily-remote-mcp)
- `tavily_search_tavily-remote-mcp` - 实时网络搜索
- `tavily_extract_tavily-remote-mcp` - 网页内容提取
- `tavily_crawl_tavily-remote-mcp` - 网站批量爬取
- `tavily_map_tavily-remote-mcp` - 网站结构发现

### 🛠️ **内置工具** (核心系统工具) - 基础功能工具集

**核心触发条件：**
- 基础文件操作、进程管理、任务管理
- 代码分析、网络搜索、可视化

#### 文件操作类
- **`str-replace-editor`** - 文件编辑器
  - **触发**：`修改文件`、`编辑代码`、`替换内容`
  - **场景**：精确的文件内容修改
  - **特点**：支持精确的查找替换操作

- **`save-file`** - 保存文件
  - **触发**：`创建文件`、`保存内容`、`新建文件`
  - **场景**：创建新文件或保存内容到文件
  - **限制**：最多300行内容

- **`remove-files`** - 删除文件
  - **触发**：`删除文件`、`移除文件`
  - **场景**：安全删除文件，支持撤销
  - **安全性**：只能删除工作区内文件

- **`view`** - 查看文件和目录
  - **触发**：`查看文件`、`显示内容`、`列出目录`
  - **场景**：查看文件内容或目录结构
  - **功能**：支持正则搜索和范围查看

#### 代码分析类
- **`codebase-retrieval`** - 代码库检索
  - **触发**：`理解代码`、`分析架构`、`代码搜索`
  - **场景**：高层次理解代码库结构
  - **特点**：AI驱动的智能代码理解

- **`diagnostics`** - IDE诊断
  - **触发**：`检查错误`、`诊断问题`
  - **场景**：获取IDE的错误和警告信息
  - **用途**：代码质量检查和问题定位

#### 进程管理类
- **`launch-process`** - 启动进程
  - **触发**：`运行命令`、`执行脚本`、`启动程序`
  - **场景**：执行终端命令或启动程序
  - **模式**：支持等待和后台运行

- **`read-process`** - 读取进程输出
- **`write-process`** - 写入进程
- **`kill-process`** - 终止进程
- **`list-processes`** - 列出进程
  - **触发**：`查看进程`、`管理进程`、`进程交互`
  - **场景**：进程监控和交互管理

#### 终端操作类
- **`read-terminal`** - 读取终端
  - **触发**：`查看终端输出`、`读取结果`
  - **场景**：获取终端的当前输出内容

#### 网络功能类
- **`web-search`** - 网络搜索
  - **触发**：`搜索`、`查找信息`、`网络搜索`
  - **场景**：使用Google搜索获取信息
  - **返回**：Markdown格式的搜索结果

- **`web-fetch`** - 网页获取
  - **触发**：`获取网页`、`抓取页面`
  - **场景**：获取指定URL的网页内容
  - **返回**：Markdown格式的页面内容

- **`open-browser`** - 打开浏览器
  - **触发**：`打开网页`、`浏览器打开`
  - **场景**：在默认浏览器中打开URL
  - **注意**：不返回内容，仅供用户查看

#### 任务管理类
- **`view_tasklist`** - 查看任务列表
- **`add_tasks`** - 添加任务
- **`update_tasks`** - 更新任务
- **`reorganize_tasklist`** - 重组任务列表
  - **触发**：`任务管理`、`项目规划`、`进度跟踪`
  - **场景**：复杂项目的任务管理和进度追踪

#### 记忆功能类
- **`remember`** - 记忆功能
  - **触发**：`记住这个`、`保存信息`
  - **场景**：跨会话的信息存储和记忆
  - **用途**：长期记忆管理

#### 可视化工具类
- **`render-mermaid`** - Mermaid图表渲染
  - **触发**：`绘制图表`、`创建流程图`、`可视化`
  - **场景**：创建各种类型的图表和图形
  - **支持**：流程图、时序图、甘特图等

#### 内容处理类
- **`view-range-untruncated`** - 查看截断内容范围
- **`search-untruncated`** - 搜索截断内容
  - **触发**：`查看完整内容`、`搜索长文本`
  - **场景**：处理被截断的长内容
  - **用途**：完整内容的查看和搜索

**工具串联模式：**
```
codebase-retrieval → view → str-replace-editor → launch-process
(理解代码 → 查看文件 → 修改代码 → 测试运行)

web-search → web-fetch → save-file → remember
(搜索信息 → 获取内容 → 保存文件 → 记忆要点)
```

## 📊 工具统计

### 按MCP工具分组统计
- **browser (Playwright)**: 24个工具
- **github**: 23个工具
- **Desktop_Commander**: 20个工具 🆕
- **firecrawl**: 8个工具
- **puppeteer**: 7个工具
- **promptx**: 7个工具
- **API文档工具**: 6个工具
- **tavily**: 4个工具 🆕
- **context7**: 2个工具
- **brave**: 2个工具
- **deepwiki**: 1个工具 🆕
- **web_search_exa**: 1个工具
- **sequentialthinking**: 1个工具
- **内置工具**: 21个工具

### 功能分类统计
- **文件和系统操作**: 45个工具 (GitHub + Desktop_Commander + 内置文件工具)
- **网页自动化**: 31个工具 (Playwright + Puppeteer)
- **网络搜索和抓取**: 15个工具 (firecrawl + tavily + brave + exa + 内置搜索)
- **进程和任务管理**: 15个工具 (Desktop_Commander + 内置进程工具)
- **AI认知增强**: 8个工具 (promptx + sequentialthinking)
- **文档和API**: 9个工具 (API文档 + context7 + deepwiki)
- **其他实用工具**: 4个工具

**总计**: 127个工具

---

## 🔗 工具组合模式（基于深度实践经验）

### 📊 常见工作流模式

#### 1. 代码分析与修改流程
```
sequentialthinking → codebase-retrieval → view → str-replace-editor → launch-process
(思考分析 → 理解代码 → 查看文件 → 修改代码 → 测试验证)
```

**触发场景：**
- "分析这个bug并修复"
- "优化这段代码的性能"
- "重构这个模块"

#### 2. 网络研究与内容整理流程
```
web-search → firecrawl_scrape → firecrawl_extract → save-file → remember
(搜索资料 → 抓取内容 → 提取数据 → 保存文件 → 记忆要点)
```

**触发场景：**
- "研究某个技术的最新发展"
- "收集竞品分析资料"
- "整理行业报告"

#### 3. GitHub协作开发流程
```
search_repositories → fork_repository → create_branch → str-replace-editor → create_pull_request
(搜索项目 → Fork仓库 → 创建分支 → 修改代码 → 创建PR)
```

**触发场景：**
- "为开源项目贡献代码"
- "修复GitHub上的bug"
- "添加新功能"

#### 4. 自动化测试流程
```
codebase-retrieval → launch-process → browser_navigate → browser_click → browser_take_screenshot
(理解代码 → 启动应用 → 打开页面 → 执行操作 → 截图验证)
```

**触发场景：**
- "测试网页功能"
- "验证用户界面"
- "端到端测试"

#### 5. 任务管理与执行流程
```
sequentialthinking → add_tasks → update_tasks → launch-process → update_tasks
(规划任务 → 创建任务 → 开始执行 → 运行操作 → 更新状态)
```

**触发场景：**
- "制定项目计划"
- "管理开发任务"
- "追踪进度"

### 🎯 智能工具选择策略（基于12工具协同矩阵）

#### 按任务复杂度分级（基于SMART-3协作规则）
- **简单任务**（<5分钟）：直接工具调用，单一或少量组合
- **中等任务**（5-30分钟）：启用Shrimp Task Manager进行任务管理
- **复杂任务**（>30分钟）：完整PER循环 + 全工具矩阵协同

#### 核心三元组（⭐⭐⭐ 优先级最高）
1. **Shrimp Task Manager** - 智能任务中枢
   - 调用条件：任务需要分解时（>3个步骤）
   - 核心功能：plan_task、analyze_task、split_tasks、execute_task、verify_task

2. **Knowledge Graph Memory** - 四维记忆引擎
   - 强制执行：每次对话开始检索记忆，结束时更新记忆图谱
   - 核心概念：实体、关系、观察、成功模式记录

3. **Feedback-Enhanced** - 用户交互核心
   - 强制调用：每轮主要响应后必须调用
   - AUTO模式：用户短时无交互时自动按计划推进

#### 并行执行优化规则

##### 信息获取并行组合
```yaml
标准组合: Context7（技术文档）+ Tavily（实时信息）
增强组合: + DeepWiki（项目理解）+ Sequential Thinking（深度分析）
记忆增强: + Knowledge Graph（历史经验）
```

##### 开发实现并行组合
```yaml
基础开发: Filesystem（文件操作）+ Context7（API文档）
全栈开发: + Playwright（前端测试）+ DBHub（数据层）
智能开发: + PromptX（专家角色）+ Knowledge Graph（模式复用）
```

##### 任务管理并行组合
```yaml
简单管理: Shrimp Task Manager + Time Server
交互管理: + Feedback-Enhanced（用户确认）
智能管理: + Knowledge Graph（经验复用）+ Sequential Thinking（优化分析）
```

#### 按数据来源选择
- **本地数据**：`view`、`codebase-retrieval`、`Desktop_Commander`
- **网络数据**：`web-search`、`firecrawl_*`、`tavily_*`
- **GitHub数据**：`search_*_github`、`get_*_github`
- **API文档**：`context7`、`read_project_oas_*`

#### 按输出格式选择
- **文本内容**：`web-search`、`firecrawl_scrape`
- **结构化数据**：`firecrawl_extract`、`context7`
- **文件操作**：`save-file`、`str-replace-editor`
- **视觉内容**：`browser_take_screenshot`、`render-mermaid`

#### 工具组合决策矩阵
| 任务类型 | 核心工具组合 | 协同策略 | 执行模式 |
|---------|-------------|----------|----------|
| 信息搜索 | Context7 + Tavily + DeepWiki | 并行获取 | 快速模式 |
| 代码开发 | Filesystem + Playwright + Context7 | 开发+测试+文档 | 标准PER |
| 数据分析 | DBHub + Sequential Thinking + Tavily | 查询+分析+趋势 | 深度模式 |
| 项目管理 | Shrimp + Knowledge Graph + Time | 规划+记忆+调度 | 完整PER |
| 专业任务 | PromptX + 相关工具组 | 角色+工具赋能 | 自适应 |

---

## ⚠️ 注意事项与最佳实践

### 🚨 错误处理和恢复机制（基于实践经验）

#### 工具失败处理策略
1. **自动重试机制**
   - 网络相关工具失败时自动重试1次
   - 重试间隔：2-5秒，避免频繁请求
   - 记录重试次数和失败原因

2. **备选方案切换**
   ```
   主要工具失败 → 自动切换备选工具
   web-search失败 → firecrawl_search
   context7-mcp失败 → deepwiki-mcp
   playwright失败 → puppeteer
   ```

3. **降级处理策略**
   - **MCP工具不可用**：自动切换到内置工具
   - **网络服务故障**：使用本地工具备选方案
   - **配额限制**：Context7等有限制时的替代策略
   - **性能降级**：复杂任务简化但保证核心功能

4. **用户询问机制**
   - 无法自动恢复时使用反馈工具询问用户
   - 提供多种解决方案供用户选择
   - 记录用户偏好用于后续类似情况

#### 场景驱动的工具选择策略

##### 🔧 代码理解与编辑场景（强制流程）
```
1. codebase-retrieval (必需) - 理解现有代码
2. view (可选) - 查看具体文件细节
3. str-replace-editor (编辑时) - 精确修改代码
4. diagnostics (验证时) - 检查问题
5. 反馈确认 (完成前) - 请求用户反馈
```

##### 🤔 复杂问题分析场景（强制流程）
```
1. sequentialthinking - 逐步分析
2. codebase-retrieval (代码相关时) - 了解现状
3. 反馈确认 (方案选择时) - 询问用户偏好
4. 其他工具 (按需) - 补充信息
```

##### 🌍 网络信息研究场景（优先级顺序）
```
1. web-search (快速搜索)
2. firecrawl_search (深度搜索)
3. firecrawl_scrape (特定页面)
4. 反馈确认 (结果确认) - 询问是否满足需求
```

### 🛡️ 安全性原则
1. **文件操作安全**
   - 使用`save-file`创建新文件
   - 使用`str-replace-editor`精确修改
   - 避免直接删除重要文件
   - 大型文件处理采用分步执行策略

2. **网络请求安全**
   - 验证URL的合法性
   - 注意抓取频率限制
   - 遵守网站robots.txt规则
   - 实施重试机制和错误处理

3. **代码执行安全**
   - 使用`launch-process`时检查命令安全性
   - 避免执行未知或危险命令
   - 在沙箱环境中测试
   - 进程失败时提供详细错误信息

### 📊 质量保证与容错机制（基于实践经验）

#### 🎯 执行标准
- **工具调用成功率**：>95%（有备用方案）
- **并行执行率**：>90%（最大化效率）
- **记忆更新率**：100%（强制执行）
- **用户满意度**：>95%（实时监控）

#### 🛡️ 容错处理
- **MCP工具不可用**：自动切换到内置工具对应功能
- **网络服务故障**：使用本地工具备选方案
- **配额限制**：Context7等有限制时的替代策略
- **性能降级**：复杂任务简化但保证核心功能

#### ⚙️ 自适应机制
- **工具性能监控**：响应时间、成功率、用户反馈
- **组合优化**：基于历史数据调整工具组合策略
- **学习改进**：Knowledge Graph记录最佳实践模式

#### 🔄 PER执行循环质量控制

##### P-PLAN（规划阶段 30%）
1. **Knowledge Graph**：检索相关历史经验
2. **PromptX评估**：是否需要创建专业角色？
3. **知识三角并行**：Context7 + Tavily + DeepWiki
4. **Sequential Thinking**：复杂任务深度分析
5. **Shrimp Task Manager**：任务分解和规划
6. **Feedback-Enhanced**：用户确认和环境检测

##### E-EXECUTE（执行阶段 50%）
1. **最大化并行**：所有独立操作同时进行
2. **工具链协同**：Filesystem + Playwright（开发+测试）
3. **实时监控**：Shrimp跟踪进度，Feedback获取反馈
4. **增量交付**：完成即展示，快速迭代

##### R-REVIEW（评审阶段 20%）
1. **Shrimp verify_task**：质量评分和验证
2. **Playwright测试**：自动化功能验证
3. **Knowledge Graph**：更新成功模式和经验
4. **Feedback-Enhanced**：最终满意度确认

### 🚀 性能优化建议
1. **工具选择优化**
   - 优先使用专用工具而非通用工具
   - 避免重复调用相同功能的工具
   - 合理使用缓存和记忆功能
   - 根据任务复杂度智能选择工具组合

2. **并行处理**
   - 独立任务可以并行执行
   - 利用异步工具（如`firecrawl_crawl`）
   - 避免不必要的等待
   - 最大化并行执行率（目标>90%）

3. **资源管理**
   - 及时关闭不需要的浏览器标签
   - 清理临时文件和进程
   - 监控系统资源使用
   - 实施智能资源调度

### 📝 用户体验优化
1. **清晰的反馈**
   - 说明正在使用的工具和原因
   - 提供操作进度和状态更新
   - 解释工具选择的逻辑

2. **错误处理**
   - 提供明确的错误信息
   - 建议替代方案
   - 支持重试机制

3. **结果呈现**
   - 结构化展示结果
   - 突出关键信息
   - 提供后续操作建议

### 🔄 持续改进
1. **学习用户偏好**
   - 使用`remember`记录用户习惯
   - 根据反馈调整工具选择
   - 优化工作流程

2. **工具更新跟踪**
   - 关注新工具的添加
   - 学习工具的新功能
   - 淘汰过时的使用方式

---

## 📈 工具统计总览

### 按功能分类
- **思维与分析**: 8个工具 (sequentialthinking + promptx思维类)
- **文件与代码**: 25个工具 (内置文件工具 + Desktop_Commander文件类)
- **网络与搜索**: 20个工具 (各类搜索和抓取工具)
- **浏览器自动化**: 31个工具 (Playwright + Puppeteer)
- **GitHub集成**: 25个工具 (完整的GitHub操作)
- **API文档**: 9个工具 (各类API文档查询)
- **任务管理**: 9个工具 (任务和记忆管理)

**总计**: 127个工具，覆盖AI助手的所有核心功能需求

---

## 📚 实践经验来源

本手册基于以下深度实践经验构建：

### 核心理论框架
- **RIPER-5模式**：6阶段工作流程（研究→构思→计划→执行→评审→快速）
- **SMART-3协作规则**：12工具协同矩阵 × 双模态响应 × 智能记忆
- **Vibe Coding理念**：自然语言驱动，减少认知负担，快速原型迭代
- **寸止协议**：强制交互规则，确保用户控制权
- **Augment协调协议**：智能模式协调，任务复杂度自适应

### 实践模板来源
- **Augment Agent工具集标准化工作流**：6模式工作流程和MCP服务优先级
- **SMART-3 MCP生态优化版**：12工具协同矩阵和并行执行规则
- **Vibe Coding增强配置**：快速原型模式和自然语言编程
- **知识顾问系统**：苏格拉底式深度探询和结构化质询
- **元提示词工程**：4-D方法论和多样化策略库

### 错误处理经验
- **工具失败处理**：自动重试、备选方案、降级策略
- **场景驱动选择**：代码编辑、问题分析、网络研究的强制流程
- **质量保证机制**：执行标准、容错处理、自适应学习

### 协作模式实践
- **多角色协作**：PM、PDM、AR、LD、DW的分工协作机制
- **任务管理集成**：Shrimp Task Manager的任务分解和依赖管理
- **记忆驱动优化**：Knowledge Graph的经验积累和模式复用

---

*最后更新时间: 2025-07-17*
*版本: v3.0 - 基于深度实践经验的AI工具调用手册*
*实践经验来源: 13-Prompt-Design目录下50+个专业模板和协议*
