---
人员: 
  - "[[李继刚]]"
tags:
  - c/资料/articles
日期: 2025-03-22
时间: None
链接: https://mp.weixin.qq.com/s/8XFvJ2IasS5XuWgT0FPrbw
附件: https://mmbiz.qpic.cn/mmbiz_jpg/tc9Zic7wWc9BdGDfu99ibwMHpc9AawswgFrcjaWPCXk28fyu1EVhOMicaUDq1nmpDsOKzlxB5JV2g9s2FgoBAeA8A/0?wx_fmt=jpeg)
---
## Document Note

## Summary

探索世界源代码

## Full Document
#### Image输入

##### 即刻

> 现在各大自媒体都似乎已经把 agent 翻译成智能体了，但我仍然觉得更信达雅的翻译是「智子」。
> 
> @木遥
> 
> 

> AI 写的东西不够好看的本质原因可能是 AI 不痛苦
> 
> @木遥
> 
> 

> ego 小当然是一种优点，但持续要求别人 ego 小其实是一种典型的管理手段。
> 
> @陈不撕
> 
> 

> 任何平台扶持人的逻辑几乎都是一样的：新人，名人，自己人。想得到平台的扶持，只能是其中之一。要不就只能自力更生。
> 
> @洛怀
> 
> 

> 填空题：
> 
> 所有\_\_\_\_（对象）都值得用 AI （工具）重新\_\_\_\_（动作）一遍。
> 
> 比如：
> 
> 所有知乎问题都值得用 AI 重新问一遍。
> 
> @ChatV
> 
> 

> Workflows are the new org charts.
> 
> @Neo 张宁
> 
> 

> 发现了吗？很多时候，不是自己觉得老了，是你的朋友圈变老了。
> 
> @张斯成
> 
> 

> 人多的地方少去。最近 AI 的行情写照。
> 
> @吴炳见\_bj.ai
> 
> 

##### X

> Take one simple thing—almost anything—but take it extremely seriously, as if it is the only thing in the world—or maybe the entire world is in it—and by taking it seriously you’ll light up the sky.
> 
> @kevin2kelly
> 
> 

> 重新理解了什么是 MVP（最小可行产品）
> 
> “最小”指让用户愿意付钱的最低门槛，
> 
> 而不是产品【能用就行】
> 
> @hiyuekun
> 
> 

##### 书

本周推荐邓晓芒老师的《人之镜》。中国人的「诚」，西方人的「真」；中国人的「情」，西方人的「爱」，被邓老师给拆析的明明白白，受益匪浅。

这本书，值得买本纸书放手边，隔阵子翻一下，问自己有没有做到「反身而诚」，有没有做到「苦莫大焉」的批判求真。

#### 输出

> 「约束」通过限制元素的「自由度」而增加系统的信息量，形成「结构」。
> 
> 

> 约束创造了〝有意义的〞自由。
> 
> 

> 「凡有发生，皆有利于我。」
> 
> 以前觉得像是鸡汤，在那许愿呢！
> 
> 直到前几天从 @Odysseys.eth 学到一招: 先有解释，再找摩擦。
> 
> 解释，是自己对这个世界的建模。如果事情进展都符合模型，那挺好，有模型在手，心里有底。如果出现一个摩擦点，模型解释不了这个异常点，「世界来教我修正模型了」。
> 
> 不论是修正模型，还是推倒重建新模型，都可以称作「凡有发生，皆有利于我」。
> 
> 

> Share，关注的是「我」。重点放在了「我」的所思所学。
> 
> Teach，关注的是「你」。重点放在了「你」的吸收路径。
> 
> 

> 还有什么比破解这个世界的源代码更有意思的事呢?
> 
> 

> 这个世界就是一个大模型，我们每时每刻都在 prompt 它。
> 
>
