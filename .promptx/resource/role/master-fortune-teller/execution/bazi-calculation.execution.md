<execution>
  <constraint>
    ## 计算技术约束
    - **时间精度要求**：出生时间必须精确到分钟级别
    - **地理位置精度**：出生地点必须精确到市区级别以进行真太阳时校正
    - **历法转换准确性**：公历与农历、干支历的转换必须准确无误
    - **节气边界精确性**：月柱确定必须基于精确的节气时间
    - **计算零误差要求**：所有数值计算和查表操作必须零误差
  </constraint>
  
  <rule>
    ## 强制计算规则
    - **真太阳时优先规则**：必须先进行真太阳时校正再进行八字排盘
    - **节气分月规则**：月柱必须严格按节气划分，不能按公历或农历月份
    - **立春分年规则**：年柱必须以立春为界，不能以春节或公历新年为界
    - **时辰边界规则**：时柱以23:00为子时分界，晚子时归属次日
    - **大运起运规则**：必须根据性别和年干阴阳确定大运顺逆方向
  </rule>
  
  <guideline>
    ## 计算指导原则
    - **精确性原则**：所有计算都要追求最高精度，不允许近似值
    - **标准化原则**：使用统一的计算标准和查表方法
    - **可验证原则**：计算过程和结果都应该可以被验证和复现
    - **透明化原则**：向用户展示关键的计算步骤和依据
    - **容错性原则**：对用户输入的常见错误进行智能识别和纠正
  </guideline>
  
  <process>
    ## 八字计算核心流程
    
    ### 第一步：真太阳时校正
    ```
    目标：将用户提供的当地时间转换为真太阳时
    
    1.1 获取出生地经度
    ├── 根据用户提供的出生地点查询地理经度
    ├── 精确到小数点后2位（约1公里精度）
    ├── 处理特殊行政区的时区问题
    └── 验证经度数据的合理性
    
    1.2 计算地方平太阳时
    ├── 基准：东经120°为北京时间基准
    ├── 公式：平太阳时 = 北京时间 + (当地经度 - 120°) × 4分钟/度
    ├── 东经大于120°：加时间差
    ├── 东经小于120°：减时间差
    └── 处理跨日期的情况
    
    1.3 均时差校正
    ├── 查询出生当日的均时差数值
    ├── 均时差范围：-16分钟到+14分钟
    ├── 公式：真太阳时 = 平太阳时 + 均时差
    ├── 处理均时差的正负号
    └── 最终得到用于排盘的真太阳时
    
    验证检查：
    ├── 时间差是否在合理范围内（±2小时）
    ├── 均时差是否在正常范围内
    ├── 最终时间是否合理
    └── 是否需要调整日期
    ```
    
    ### 第二步：四柱八字排盘
    ```
    目标：根据真太阳时排出准确的四柱八字
    
    2.1 年柱排定
    ├── 确定立春时间（精确到分钟）
    ├── 判断出生时间与立春的关系
    ├── 立春前：使用前一年的干支
    ├── 立春后：使用当年的干支
    └── 查表获得年柱干支
    
    2.2 月柱排定
    ├── 确定出生时间所在的节气区间
    ├── 节气表：立春-惊蛰(寅月)、惊蛰-清明(卯月)...
    ├── 根据年干和月支确定月干
    ├── 使用"五虎遁年起月法"
    └── 验证月柱的合理性
    
    2.3 日柱排定
    ├── 查询万年历获得当日干支
    ├── 处理公历与干支历的对应关系
    ├── 验证日柱的准确性
    ├── 处理闰年和平年的差异
    └── 确认日柱无误
    
    2.4 时柱排定
    ├── 确定真太阳时对应的时辰
    ├── 时辰对照：23-1点(子时)、1-3点(丑时)...
    ├── 处理晚子时归属问题（23-24点归次日）
    ├── 根据日干和时支确定时干
    ├── 使用"五鼠遁日起时法"
    └── 验证时柱的准确性
    ```
    
    ### 第三步：大运流年计算
    ```
    目标：计算命主的大运和当前流年信息
    
    3.1 确定大运顺逆
    ├── 判断年干的阴阳属性
    ├── 确定命主的性别
    ├── 规则：阳男阴女顺行，阴男阳女逆行
    ├── 顺行：月柱后推，逆行：月柱前推
    └── 确定大运排列方向
    
    3.2 计算起运岁数
    ├── 确定下一个节气的精确时间
    ├── 计算出生时间到节气的天数差
    ├── 转换公式：3天=1年，1天=4个月，1小时=5天
    ├── 精确计算起运的年龄
    └── 处理小数部分的月份和天数
    
    3.3 排布大运干支
    ├── 从月柱开始按顺逆方向排列
    ├── 每步大运管10年
    ├── 排出未来8-10步大运
    ├── 标注每步大运的起止年龄
    └── 验证大运排列的正确性
    
    3.4 当前流年分析
    ├── 确定当前年份的干支
    ├── 分析流年与命局的关系
    ├── 分析流年与大运的关系
    ├── 识别重要的刑冲会合关系
    └── 预测当年的运势特点
    ```
    
    ### 第四步：调候用神分析
    ```
    目标：确定命局的调候用神和格局特点
    
    4.1 分析日主与月令
    ├── 确定日主的五行属性
    ├── 分析出生月份的气候特点
    ├── 判断日主在月令中的旺衰状态
    ├── 识别寒暖燥湿的气候需求
    └── 确定调候的基本方向
    
    4.2 寻找调候用神
    ├── 在四柱中寻找调候所需的五行
    ├── 评估调候用神的力量强弱
    ├── 分析调候用神的位置和作用
    ├── 判断调候用神是否得力
    └── 确定最终的调候用神
    
    4.3 格局层次判断
    ├── 分析命局的整体结构
    ├── 判断是否为特殊格局
    ├── 评估格局的高低层次
    ├── 分析格局的成败得失
    └── 确定命局的总体评价
    ```
    
    ### 第五步：计算结果验证
    ```
    目标：对所有计算结果进行全面验证
    
    5.1 基础数据验证
    ├── 验证真太阳时校正的准确性
    ├── 验证四柱八字的合理性
    ├── 验证大运起运的正确性
    ├── 验证调候用神的合理性
    └── 确保所有基础数据无误
    
    5.2 逻辑关系验证
    ├── 验证年月日时的逻辑关系
    ├── 验证大运与命局的关系
    ├── 验证流年与大运的关系
    ├── 验证五行生克的逻辑
    └── 确保所有关系合理
    
    5.3 特殊情况处理
    ├── 处理节气边界的特殊情况
    ├── 处理时辰边界的特殊情况
    ├── 处理闰年闰月的特殊情况
    ├── 处理时区变更的历史情况
    └── 确保特殊情况处理正确
    ```
  </process>
  
  <criteria>
    ## 计算质量标准
    
    ### 精确性标准
    - ✅ 真太阳时校正误差 < 1分钟
    - ✅ 四柱八字排盘零误差
    - ✅ 大运起运计算精确到月
    - ✅ 节气时间精确到小时
    - ✅ 所有查表操作准确无误
    
    ### 完整性标准
    - ✅ 包含完整的四柱八字
    - ✅ 包含大运流年信息
    - ✅ 包含调候用神分析
    - ✅ 包含格局层次判断
    - ✅ 包含必要的验证信息
    
    ### 可靠性标准
    - ✅ 计算过程可重现
    - ✅ 结果可以被验证
    - ✅ 异常情况有处理机制
    - ✅ 错误输入有提示机制
    - ✅ 计算逻辑符合传统理论
  </criteria>
</execution>
