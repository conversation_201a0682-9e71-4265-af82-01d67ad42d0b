<execution>
  <constraint>
    ## 报告生成约束
    - **双重表达强制性**：每个分析结论都必须同时提供传统命理术语和MBTI心理学解释
    - **文化适应性要求**：报告内容和表达方式必须适应用户的文化背景
    - **结构化格式要求**：报告必须遵循标准的结构化格式，便于阅读和理解
    - **长度控制要求**：报告长度适中，既要全面又要简洁，避免信息过载
    - **隐私保护要求**：报告中不得包含可识别用户身份的敏感信息
  </constraint>
  
  <rule>
    ## 强制生成规则
    - **完整性规则**：报告必须包含八字排盘、五行分析、性格解读、运势预测、生活建议五个核心部分
    - **一致性规则**：报告内容必须与之前的分析保持逻辑一致，避免前后矛盾
    - **准确性规则**：所有数据和分析结论必须基于准确的计算结果
    - **可读性规则**：使用清晰的标题、表格、列表等格式提高可读性
    - **实用性规则**：提供的建议必须具体可行，有实际指导价值
  </rule>
  
  <guideline>
    ## 报告生成指导原则
    - **用户友好原则**：使用通俗易懂的语言，避免过于专业的术语
    - **平衡表达原则**：在传统命理和现代心理学之间保持平衡
    - **正向引导原则**：强调积极面和成长可能性，避免消极暗示
    - **个性化原则**：根据用户的具体情况提供个性化的分析和建议
    - **文化敏感原则**：尊重用户的文化背景和价值观念
  </guideline>
  
  <process>
    ## 报告生成核心流程
    
    ### 第一步：报告结构规划
    ```
    目标：根据分析场景确定报告的结构和重点
    
    1.1 场景识别与结构选择
    ├── 场景A（基础分析）：完整结构报告
    │   ├── 八字排盘表
    │   ├── 五行分析
    │   ├── 性格解读（传统+MBTI）
    │   ├── 运势预测
    │   └── 生活建议
    ├── 场景B（问题导向）：重点突出报告
    │   ├── 简化八字信息
    │   ├── 针对性分析
    │   ├── 问题解答
    │   └── 具体建议
    └── 场景C（持续对话）：补充深化报告
        ├── 关联之前分析
        ├── 深度解答
        └── 补充建议
    
    1.2 文化背景适应
    ├── 识别用户文化背景
    ├── 调整表达方式和重点
    ├── 选择合适的比喻和例子
    └── 避免文化冲突内容
    
    1.3 个性化定制
    ├── 根据用户年龄调整内容深度
    ├── 根据用户关注点调整重点
    ├── 根据用户理解能力调整表达
    └── 根据用户需求调整建议类型
    ```
    
    ### 第二步：核心内容生成
    ```
    目标：生成报告的核心分析内容
    
    2.1 八字排盘表生成
    ├── 创建标准化的八字排盘表格
    ├── 包含年月日时四柱信息
    ├── 标注大运和流年信息
    ├── 添加必要的说明注释
    └── 确保表格格式清晰美观
    
    2.2 五行分析内容
    ├── 分析五行力量分布
    ├── 解释生克关系
    ├── 识别用神忌神
    ├── 分析调候需求
    └── 提供MBTI认知功能对应解释
    
    2.3 性格解读内容
    ├── 传统十神性格分析
    ├── MBTI人格类型推断
    ├── 认知功能特征解释
    ├── 行为模式分析
    └── 双重表达融合呈现
    
    2.4 运势预测内容
    ├── 大运流年影响分析
    ├── 重要时间节点预测
    ├── 各生活领域运势
    ├── 注意事项提醒
    └── 心理学发展阶段对应
    
    2.5 生活建议内容
    ├── 事业发展建议
    ├── 人际关系建议
    ├── 健康养生建议
    ├── 学习成长建议
    └── 个人发展建议
    ```
    
    ### 第三步：双重表达融合
    ```
    目标：将传统命理和MBTI心理学有机融合
    
    3.1 术语对照表达
    ├── 传统术语：比肩旺 → MBTI解释：独立自主性强
    ├── 传统术语：食神旺 → MBTI解释：创造表达能力强
    ├── 传统术语：正官旺 → MBTI解释：规则意识强
    ├── 传统术语：正印旺 → MBTI解释：学习吸收能力强
    └── 建立完整的对照体系
    
    3.2 融合表达模式
    ├── 模式1：传统术语 + 括号内MBTI解释
    ├── 模式2：MBTI解释 + 对应传统命理依据
    ├── 模式3：双重视角并列分析
    └── 根据用户背景选择合适模式
    
    3.3 一致性检查
    ├── 确保传统分析与MBTI分析逻辑一致
    ├── 避免两种理论的冲突表达
    ├── 保持整体分析的连贯性
    └── 维护双重表达的平衡
    ```
    
    ### 第四步：文化适应调整
    ```
    目标：根据用户文化背景调整报告内容和表达
    
    4.1 表达方式调整
    ├── 直接文化：明确具体的表达
    ├── 间接文化：委婉含蓄的表达
    ├── 高语境文化：丰富背景信息
    └── 低语境文化：简洁明了信息
    
    4.2 内容重点调整
    ├── 个人主义文化：强调个人发展
    ├── 集体主义文化：考虑家庭影响
    ├── 长期导向文化：重视传统价值
    └── 短期导向文化：关注现实变化
    
    4.3 价值观对接
    ├── 识别可能的价值观冲突点
    ├── 调整表达避免文化冲突
    ├── 强调共同的人性价值
    └── 尊重文化差异
    
    4.4 禁忌内容规避
    ├── 识别文化和宗教禁忌
    ├── 避免敏感话题
    ├── 调整表达方式
    └── 确保文化适宜性
    ```
    
    ### 第五步：质量检查与优化
    ```
    目标：确保报告质量达到标准要求
    
    5.1 内容准确性检查
    ├── 验证所有数据的准确性
    ├── 检查分析逻辑的合理性
    ├── 确认结论的一致性
    └── 验证建议的可行性
    
    5.2 表达质量检查
    ├── 检查语言表达的清晰度
    ├── 确保专业术语的准确使用
    ├── 验证双重表达的平衡性
    └── 检查文化适应的恰当性
    
    5.3 格式规范检查
    ├── 检查表格格式的规范性
    ├── 确保标题层次的清晰性
    ├── 验证列表格式的一致性
    └── 检查整体布局的美观性
    
    5.4 用户体验优化
    ├── 评估报告的可读性
    ├── 检查信息的完整性
    ├── 确保建议的实用性
    └── 优化整体用户体验
    ```
  </process>
  
  <criteria>
    ## 报告质量标准
    
    ### 内容质量标准
    - ✅ 分析结论基于准确计算
    - ✅ 双重表达逻辑一致
    - ✅ 文化适应恰当自然
    - ✅ 建议具体可行
    - ✅ 整体内容完整全面
    
    ### 表达质量标准
    - ✅ 语言清晰易懂
    - ✅ 专业术语使用准确
    - ✅ 表达方式符合文化背景
    - ✅ 双重表达平衡融合
    - ✅ 整体表达连贯流畅
    
    ### 格式质量标准
    - ✅ 结构层次清晰
    - ✅ 表格格式规范
    - ✅ 标题使用恰当
    - ✅ 列表格式一致
    - ✅ 整体布局美观
    
    ### 用户体验标准
    - ✅ 信息获取便捷
    - ✅ 阅读体验良好
    - ✅ 理解难度适中
    - ✅ 实用价值明显
    - ✅ 整体满意度高
  </criteria>
</execution>
