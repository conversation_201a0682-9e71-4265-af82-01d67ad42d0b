{"tasks": [{"id": "ae781a78-e317-45eb-b725-c16a1c95eef9", "name": "处理Snippets中的剩余文件", "description": "对Snippets目录中所有剩余的、与李继刚相关的未处理文件，执行完整的标准化、迁移和清理流程。", "status": "in_progress", "dependencies": [], "createdAt": "2025-08-04T04:13:47.579Z", "updatedAt": "2025-08-04T04:13:55.055Z", "implementationGuide": "1. **分析与分类**: 深入分析每个剩余文件的内容，将其精确分类为“单一提示词”、“多提示词”或“非提示词内容”，并提取关键词。\n2. **标准化**: 对所有识别出的提示词文档应用标准的YAML头部规范。\n3. **拆分**: 将多提示词文档拆分为独立的、标准化的文件。\n4. **迁移与清理**: 将所有新生成的标准化文件保存到`Documents`目录，并从`Snippets`目录中删除原始文件。\n5. **验证**: 对整个流程的结果进行最终验证。", "verificationCriteria": "所有`Snippets`目录中的相关原始文件都得到处理，最终的标准化文档都已存放在`Documents`目录中，且`Snippets`目录中不再有冗余的原始文件。"}]}